package com.fasnote.alm.injection.processor;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.injection.api.IDependencyInjector;
import com.fasnote.alm.injection.api.IPostProcessor;

/**
 * 循环依赖后置处理器
 * 专门处理循环依赖问题，在所有单例服务创建完成后自动连接依赖关系
 */
public class CircularDependencyPostProcessor implements IPostProcessor {

    private static final Logger logger = LoggerFactory.getLogger(CircularDependencyPostProcessor.class);

    /**
     * 循环依赖配置
     */
    public static class CircularDependencyConfig {
        private final Class<?> sourceClass;
        private final String setterMethodName;
        private final Class<?> targetClass;

        public CircularDependencyConfig(Class<?> sourceClass, String setterMethodName, Class<?> targetClass) {
            this.sourceClass = sourceClass;
            this.setterMethodName = setterMethodName;
            this.targetClass = targetClass;
        }

        public Class<?> getSourceClass() { return sourceClass; }
        public String getSetterMethodName() { return setterMethodName; }
        public Class<?> getTargetClass() { return targetClass; }
    }

    private final List<CircularDependencyConfig> circularDependencies = new ArrayList<>();

    /**
     * 注册循环依赖配置
     * 
     * @param sourceClass 源类（需要注入依赖的类）
     * @param setterMethodName setter方法名
     * @param targetClass 目标类（要注入的依赖类）
     */
    public void registerCircularDependency(Class<?> sourceClass, String setterMethodName, Class<?> targetClass) {
        circularDependencies.add(new CircularDependencyConfig(sourceClass, setterMethodName, targetClass));
        logger.debug("注册循环依赖配置: {} -> {} (通过 {})", 
                    sourceClass.getSimpleName(), targetClass.getSimpleName(), setterMethodName);
    }

    @Override
    public void postProcessAfterSingletonCreation(IDependencyInjector injector) {
        logger.info("开始处理循环依赖，配置数量: {}", circularDependencies.size());

        for (CircularDependencyConfig config : circularDependencies) {
            try {
                processCircularDependency(injector, config);
            } catch (Exception e) {
                logger.error("处理循环依赖失败: {} -> {}", 
                           config.getSourceClass().getSimpleName(), 
                           config.getTargetClass().getSimpleName(), e);
            }
        }

        logger.info("循环依赖处理完成");
    }

    /**
     * 处理单个循环依赖配置
     */
    private void processCircularDependency(IDependencyInjector injector, CircularDependencyConfig config) 
            throws Exception {
        
        // 获取源对象实例
        Object sourceInstance = injector.getService(config.getSourceClass());
        if (sourceInstance == null) {
            logger.warn("无法获取源对象实例: {}", config.getSourceClass().getName());
            return;
        }

        // 获取目标对象实例
        Object targetInstance = injector.getService(config.getTargetClass());
        if (targetInstance == null) {
            logger.warn("无法获取目标对象实例: {}", config.getTargetClass().getName());
            return;
        }

        // 查找setter方法
        Method setterMethod = findSetterMethod(config.getSourceClass(), config.getSetterMethodName(), config.getTargetClass());
        if (setterMethod == null) {
            logger.error("无法找到setter方法: {}.{}({})", 
                        config.getSourceClass().getName(), 
                        config.getSetterMethodName(), 
                        config.getTargetClass().getName());
            return;
        }

        // 调用setter方法注入依赖
        setterMethod.setAccessible(true);
        setterMethod.invoke(sourceInstance, targetInstance);

        logger.debug("成功注入循环依赖: {}.{}({})", 
                    config.getSourceClass().getSimpleName(), 
                    config.getSetterMethodName(), 
                    config.getTargetClass().getSimpleName());
    }

    /**
     * 查找setter方法
     */
    private Method findSetterMethod(Class<?> sourceClass, String methodName, Class<?> parameterType) {
        try {
            // 首先尝试精确匹配
            return sourceClass.getMethod(methodName, parameterType);
        } catch (NoSuchMethodException e) {
            // 如果精确匹配失败，尝试查找兼容的方法
            Method[] methods = sourceClass.getMethods();
            for (Method method : methods) {
                if (method.getName().equals(methodName) && 
                    method.getParameterCount() == 1) {
                    
                    Class<?> paramType = method.getParameterTypes()[0];
                    if (paramType.isAssignableFrom(parameterType)) {
                        return method;
                    }
                }
            }
        }
        return null;
    }

    @Override
    public int getPriority() {
        // 循环依赖处理器应该有较高的优先级
        return 10;
    }

    @Override
    public String getName() {
        return "CircularDependencyPostProcessor";
    }

    /**
     * 获取已注册的循环依赖配置数量
     */
    public int getConfigCount() {
        return circularDependencies.size();
    }

    /**
     * 清除所有循环依赖配置（主要用于测试）
     */
    public void clearConfigs() {
        circularDependencies.clear();
        logger.debug("已清除所有循环依赖配置");
    }
}
