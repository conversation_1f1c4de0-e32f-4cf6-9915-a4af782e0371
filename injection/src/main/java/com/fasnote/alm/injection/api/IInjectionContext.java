package com.fasnote.alm.injection.api;

import java.util.Map;

/**
 * 依赖注入上下文接口
 * 提供服务创建过程中的上下文信息
 */
public interface IInjectionContext {
    
    /**
     * 获取依赖注入器实例
     * 
     * @return 依赖注入器
     */
    IDependencyInjector getInjector();
    
    /**
     * 获取当前正在创建的服务类
     * 
     * @return 服务类
     */
    Class<?> getCurrentServiceClass();
    
    /**
     * 获取服务名称（如果有）
     * 
     * @return 服务名称
     */
    String getServiceName();
    
    /**
     * 获取上下文属性
     * 
     * @param key 属性键
     * @return 属性值
     */
    Object getAttribute(String key);
    
    /**
     * 设置上下文属性
     * 
     * @param key 属性键
     * @param value 属性值
     */
    void setAttribute(String key, Object value);
    
    /**
     * 获取所有上下文属性
     * 
     * @return 属性映射
     */
    Map<String, Object> getAttributes();
    
    /**
     * 创建依赖对象
     *
     * @param dependencyClass 依赖类
     * @return 依赖实例
     */
    <T> T createDependency(Class<T> dependencyClass) throws Exception;
}