package com.fasnote.alm.injection.api;

/**
 * 后置处理器接口
 * 用于在依赖注入完成后执行额外的处理逻辑
 * 
 * 主要用途：
 * 1. 解决循环依赖问题
 * 2. 执行复杂的初始化逻辑
 * 3. 设置代理对象
 * 4. 执行验证逻辑
 */
public interface IPostProcessor {

    /**
     * 在实例创建后、依赖注入前执行
     * 
     * @param instance 创建的实例
     * @param serviceClass 服务类
     * @return 处理后的实例（可以是原实例或代理实例）
     */
    default Object postProcessBeforeInjection(Object instance, Class<?> serviceClass) {
        return instance;
    }

    /**
     * 在依赖注入完成后执行
     * 
     * @param instance 注入完成的实例
     * @param serviceClass 服务类
     * @return 处理后的实例（可以是原实例或代理实例）
     */
    default Object postProcessAfterInjection(Object instance, Class<?> serviceClass) {
        return instance;
    }

    /**
     * 在所有单例服务创建完成后执行
     * 这是解决循环依赖的最佳时机
     * 
     * @param injector 依赖注入器实例
     */
    default void postProcessAfterSingletonCreation(IDependencyInjector injector) {
        // 默认不执行任何操作
    }

    /**
     * 是否应该处理指定的服务类
     * 
     * @param serviceClass 服务类
     * @return true表示需要处理，false表示跳过
     */
    default boolean shouldProcess(Class<?> serviceClass) {
        return true;
    }

    /**
     * 获取处理器优先级（数字越小优先级越高）
     * 
     * @return 优先级
     */
    default int getPriority() {
        return 100;
    }

    /**
     * 获取处理器名称（用于调试和日志）
     * 
     * @return 处理器名称
     */
    default String getName() {
        return this.getClass().getSimpleName();
    }
}
