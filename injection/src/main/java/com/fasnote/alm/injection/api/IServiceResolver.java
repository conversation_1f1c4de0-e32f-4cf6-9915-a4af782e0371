package com.fasnote.alm.injection.api;

/**
 * 服务解析器接口
 * 
 * 类似于 Spring 的 BeanPostProcessor，用于在服务解析阶段进行干预
 * 允许根据业务逻辑（如许可证状态）选择不同的实现类
 */
public interface IServiceResolver {
    
    /**
     * 是否应该处理指定的服务类型
     * 
     * @param serviceClass 服务接口类型
     * @return true表示需要处理
     */
    boolean shouldResolve(Class<?> serviceClass);
    
    /**
     * 解析服务实现类
     * 
     * 在服务解析阶段被调用，用于选择合适的实现类
     * 
     * @param serviceClass 服务接口类型
     * @param serviceName 服务名称（可能为null）
     * @param defaultImplementation 默认实现类（可能为null）
     * @return 选择的实现类，如果返回null则使用默认逻辑
     */
    Class<?> resolveImplementation(Class<?> serviceClass, String serviceName, Class<?> defaultImplementation);
    
    /**
     * 解析器优先级（数字越小优先级越高）
     * 
     * @return 优先级
     */
    default int getPriority() {
        return 100;
    }
}
