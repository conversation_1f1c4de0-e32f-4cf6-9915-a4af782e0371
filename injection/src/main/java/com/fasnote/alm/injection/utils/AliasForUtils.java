package com.fasnote.alm.injection.utils;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.injection.annotations.AliasFor;

/**
 * AliasFor工具类
 * 
 * 提供@AliasFor注解的解析和属性别名处理功能，类似于Spring的AnnotationUtils。
 * 支持属性别名的解析、值的获取和缓存优化。
 * 
 * 主要功能：
 * 1. 解析@AliasFor注解声明的别名关系
 * 2. 通过别名获取属性值
 * 3. 支持多层别名传递
 * 4. 缓存解析结果以提高性能
 * 
 * <AUTHOR> Team
 * @since 1.0
 */
public final class AliasForUtils {
    
    private static final Logger logger = LoggerFactory.getLogger(AliasForUtils.class);
    
    /**
     * 别名信息缓存
     * Key: 注解类型 + 属性名称
     * Value: 别名信息
     */
    private static final Map<String, AliasInfo> aliasCache = new ConcurrentHashMap<>();
    
    /**
     * 属性值缓存
     * Key: 注解实例 + 属性名称
     * Value: 属性值
     */
    private static final Map<String, Object> valueCache = new ConcurrentHashMap<>();
    
    private AliasForUtils() {
        // 工具类，禁止实例化
    }
    
    /**
     * 获取注解属性的值，支持@AliasFor别名解析
     * 
     * @param annotation 注解实例
     * @param attributeName 属性名称
     * @return 属性值，如果未找到返回null
     */
    public static Object getAttributeValue(Annotation annotation, String attributeName) {
        if (annotation == null || attributeName == null || attributeName.isEmpty()) {
            return null;
        }
        
        String cacheKey = annotation.toString() + "#" + attributeName;
        Object cachedValue = valueCache.get(cacheKey);
        if (cachedValue != null) {
            return cachedValue;
        }
        
        try {
            Object value = getAttributeValueInternal(annotation, attributeName);
            if (value != null) {
                valueCache.put(cacheKey, value);
            }
            return value;
        } catch (Exception e) {
            logger.debug("获取属性值失败: {}.{}", annotation.annotationType().getSimpleName(), attributeName, e);
            return null;
        }
    }
    
    /**
     * 获取指定目标注解的属性值（通过@AliasFor别名）
     * 
     * @param annotation 当前注解实例
     * @param targetAnnotationType 目标注解类型
     * @param targetAttributeName 目标属性名称
     * @return 属性值，如果未找到返回null
     */
    public static Object getAliasedAttributeValue(Annotation annotation, 
                                                Class<? extends Annotation> targetAnnotationType,
                                                String targetAttributeName) {
        if (annotation == null || targetAnnotationType == null || targetAttributeName == null) {
            return null;
        }
        
        // 查找指向目标注解属性的别名
        for (Method method : annotation.annotationType().getDeclaredMethods()) {
            AliasFor aliasFor = method.getAnnotation(AliasFor.class);
            if (aliasFor != null) {
                String aliasAttribute = getAliasAttributeName(aliasFor);
                Class<? extends Annotation> aliasAnnotation = aliasFor.annotation();
                
                // 检查是否指向目标注解的目标属性
                if (aliasAnnotation.equals(targetAnnotationType) && 
                    targetAttributeName.equals(aliasAttribute)) {
                    
                    try {
                        return method.invoke(annotation);
                    } catch (Exception e) {
                        logger.debug("通过别名获取属性值失败: {}.{} -> {}.{}", 
                                   annotation.annotationType().getSimpleName(), method.getName(),
                                   targetAnnotationType.getSimpleName(), targetAttributeName, e);
                    }
                }
            }
        }
        
        return null;
    }
    
    /**
     * 检查注解是否有指向指定目标的别名属性
     * 
     * @param annotationType 注解类型
     * @param targetAnnotationType 目标注解类型
     * @param targetAttributeName 目标属性名称
     * @return 如果存在别名返回true，否则返回false
     */
    public static boolean hasAliasFor(Class<? extends Annotation> annotationType,
                                    Class<? extends Annotation> targetAnnotationType,
                                    String targetAttributeName) {
        if (annotationType == null || targetAnnotationType == null || targetAttributeName == null) {
            return false;
        }
        
        String cacheKey = annotationType.getName() + "#" + targetAnnotationType.getName() + "#" + targetAttributeName;
        AliasInfo cachedInfo = aliasCache.get(cacheKey);
        if (cachedInfo != null) {
            return cachedInfo.hasAlias;
        }
        
        boolean hasAlias = false;
        String aliasMethodName = null;
        
        for (Method method : annotationType.getDeclaredMethods()) {
            AliasFor aliasFor = method.getAnnotation(AliasFor.class);
            if (aliasFor != null) {
                String aliasAttribute = getAliasAttributeName(aliasFor);
                Class<? extends Annotation> aliasAnnotation = aliasFor.annotation();
                
                if (aliasAnnotation.equals(targetAnnotationType) && 
                    targetAttributeName.equals(aliasAttribute)) {
                    hasAlias = true;
                    aliasMethodName = method.getName();
                    break;
                }
            }
        }
        
        // 缓存结果
        AliasInfo aliasInfo = new AliasInfo(hasAlias, aliasMethodName);
        aliasCache.put(cacheKey, aliasInfo);
        
        return hasAlias;
    }
    
    /**
     * 获取别名方法名称
     * 
     * @param annotationType 注解类型
     * @param targetAnnotationType 目标注解类型
     * @param targetAttributeName 目标属性名称
     * @return 别名方法名称，如果不存在返回null
     */
    public static String getAliasMethodName(Class<? extends Annotation> annotationType,
                                          Class<? extends Annotation> targetAnnotationType,
                                          String targetAttributeName) {
        String cacheKey = annotationType.getName() + "#" + targetAnnotationType.getName() + "#" + targetAttributeName;
        AliasInfo cachedInfo = aliasCache.get(cacheKey);
        if (cachedInfo != null) {
            return cachedInfo.methodName;
        }
        
        // 如果缓存中没有，触发hasAliasFor来建立缓存
        hasAliasFor(annotationType, targetAnnotationType, targetAttributeName);
        
        // 再次尝试从缓存获取
        cachedInfo = aliasCache.get(cacheKey);
        return cachedInfo != null ? cachedInfo.methodName : null;
    }
    
    /**
     * 清理缓存
     */
    public static void clearCache() {
        aliasCache.clear();
        valueCache.clear();
        logger.debug("AliasFor缓存已清理");
    }
    
    /**
     * 获取缓存统计信息
     * 
     * @return 缓存统计信息
     */
    public static Map<String, Object> getCacheStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("aliasCacheSize", aliasCache.size());
        stats.put("valueCacheSize", valueCache.size());
        return stats;
    }
    
    // ==================== 私有方法 ====================
    
    private static Object getAttributeValueInternal(Annotation annotation, String attributeName) throws Exception {
        Method method = annotation.annotationType().getMethod(attributeName);
        return method.invoke(annotation);
    }
    
    private static String getAliasAttributeName(AliasFor aliasFor) {
        String value = aliasFor.value();
        if (!value.isEmpty()) {
            return value;
        }
        
        String attribute = aliasFor.attribute();
        if (!attribute.isEmpty()) {
            return attribute;
        }
        
        throw new IllegalArgumentException("@AliasFor必须指定value或attribute属性");
    }
    
    /**
     * 别名信息
     */
    private static class AliasInfo {
        final boolean hasAlias;
        final String methodName;
        
        AliasInfo(boolean hasAlias, String methodName) {
            this.hasAlias = hasAlias;
            this.methodName = methodName;
        }
    }
}
