package com.fasnote.alm.injection.annotations;

import java.lang.annotation.Annotation;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 属性别名注解
 * 
 * 类似于Spring框架中的@AliasFor，用于在组合注解中声明属性别名关系。
 * 支持将组合注解的属性映射到元注解的属性上，实现属性值的传递和统一。
 * 
 * 使用示例：
 * <pre>
 * &#64;Service
 * public &#64;interface MyComposedAnnotation {
 *     
 *     &#64;AliasFor(value = "scope", annotation = Service.class)
 *     Service.Scope lifecycle() default Service.Scope.SINGLETON;
 *     
 *     &#64;AliasFor(value = "value", annotation = Service.class)
 *     String serviceName() default "";
 * }
 * </pre>
 * 
 * 支持的别名类型：
 * 1. 显式别名：指定目标注解和属性名称
 * 2. 隐式别名：在同一注解内的属性互为别名
 * 3. 传递别名：通过多层注解传递属性值
 * 
 * <AUTHOR> Team
 * @since 1.0
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface AliasFor {

    /**
     * 别名属性名称
     * 
     * 指定在目标注解中对应的属性名称。
     * 如果未指定annotation()，则表示在当前注解内的别名属性。
     * 
     * @return 别名属性名称
     */
    String value() default "";

    /**
     * 别名属性名称（与value()等价）
     * 
     * 提供另一种指定别名属性名称的方式，与value()功能相同。
     * 当value()和attribute()同时指定时，优先使用value()。
     * 
     * @return 别名属性名称
     */
    String attribute() default "";

    /**
     * 目标注解类型
     * 
     * 指定别名属性所在的目标注解类型。
     * 如果未指定，则表示别名属性在当前注解内。
     * 
     * 注意：目标注解必须是当前注解的元注解（直接或间接）。
     * 
     * @return 目标注解类型
     */
    Class<? extends Annotation> annotation() default Annotation.class;

    /**
     * 是否为双向别名
     * 
     * 当设置为true时，属性值的变化会双向同步。
     * 当设置为false时，只从当前属性向目标属性单向传递值。
     * 
     * 默认为true，支持双向同步。
     * 
     * @return 是否为双向别名
     */
    boolean bidirectional() default true;

    /**
     * 别名优先级
     * 
     * 当存在多个别名指向同一目标属性时，使用优先级来决定使用哪个值。
     * 数字越小优先级越高。
     * 
     * 默认优先级为0。
     * 
     * @return 别名优先级
     */
    int priority() default 0;
}
