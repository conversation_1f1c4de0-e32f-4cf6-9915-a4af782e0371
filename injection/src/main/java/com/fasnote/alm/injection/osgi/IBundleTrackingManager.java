package com.fasnote.alm.injection.osgi;

import org.osgi.framework.Bundle;
import org.osgi.framework.BundleContext;

import java.util.Set;

/**
 * Bundle 跟踪管理器接口
 * 负责监听 OSGi Bundle 状态变化并触发相应的服务扫描操作
 * 
 * 采用事件驱动机制，基于 OSGi Bundle 事件来动态响应 Bundle 的加载/卸载
 */
public interface IBundleTrackingManager {
    
    /**
     * 启动 Bundle 跟踪
     * 
     * @param bundleContext OSGi Bundle 上下文
     */
    void start(BundleContext bundleContext);
    
    /**
     * 停止 Bundle 跟踪
     */
    void stop();
    
    /**
     * 检查是否正在跟踪
     * 
     * @return true 如果正在跟踪
     */
    boolean isTracking();
    
    /**
     * 获取当前跟踪的 Bundle 列表
     * 
     * @return Bundle 符号名称集合
     */
    Set<String> getTrackedBundles();
    
    /**
     * 手动触发指定 Bundle 的扫描
     * 
     * @param bundleSymbolicName Bundle 符号名称
     */
    void triggerBundleScan(String bundleSymbolicName);
    
    /**
     * Bundle 事件回调接口
     * 用于与 DependencyInjector 进行交互
     */
    interface BundleEventCallback {

        /**
         * 当 Bundle 进入可扫描状态时调用
         *
         * @param bundle Bundle 实例
         * @param scanPackages 该Bundle需要扫描的包路径
         */
        void onBundleReady(Bundle bundle, String[] scanPackages);

        /**
         * 当 Bundle 被移除时调用
         *
         * @param bundle Bundle 实例
         */
        void onBundleRemoved(Bundle bundle);
    }
    
    /**
     * 设置 Bundle 事件回调
     * 
     * @param callback 回调接口实现
     */
    void setBundleEventCallback(BundleEventCallback callback);
}
