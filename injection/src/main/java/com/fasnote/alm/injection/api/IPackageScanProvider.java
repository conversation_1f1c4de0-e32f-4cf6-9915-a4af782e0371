package com.fasnote.alm.injection.api;

/**
 * 包扫描提供者接口
 *
 * Bundle通过实现此接口来声明需要扫描的包路径，作为DI框架的基础功能。
 * 只有注册了此服务的Bundle才会被DI框架自动扫描和注册。
 */
public interface IPackageScanProvider {

    /**
     * 获取提供者名称
     *
     * @return 提供者名称
     */
    String getName();

    /**
     * 获取插件ID
     *
     * @return 插件ID
     */
    String getPluginId();

    /**
     * 获取优先级
     *
     * @return 优先级（数字越小优先级越高）
     */
    default int getPriority() {
        return 100;
    }

    /**
     * 获取需要扫描的包路径
     *
     * @return 包路径数组，如果返回空数组则表示该Bundle不参与DI扫描
     */
    String[] getScanPackages();
}
