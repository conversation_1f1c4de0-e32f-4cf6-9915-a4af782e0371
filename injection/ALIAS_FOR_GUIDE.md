# @AliasFor 属性别名机制使用指南

## 概述

@AliasFor 是我们依赖注入框架中的属性别名注解，类似于Spring框架中的@AliasFor。它用于在组合注解中声明属性别名关系，实现属性值的传递和统一。

## 核心特性

### ✅ **属性别名映射**
- 支持将组合注解的属性映射到元注解的属性
- 统一不同组合注解的属性名称
- 简化属性访问和配置

### ✅ **向后兼容性**
- 保留原有的属性名称和类型
- 支持渐进式迁移
- 不破坏现有代码

### ✅ **性能优化**
- 缓存别名解析结果
- 缓存属性值获取结果
- 减少反射调用开销

## 使用方式

### 1. 基本语法

```java
@AliasFor(value = "targetAttribute", annotation = TargetAnnotation.class)
ReturnType sourceAttribute() default defaultValue;
```

### 2. 参数说明

- **value**: 目标属性名称
- **attribute**: 与value等价，提供另一种写法
- **annotation**: 目标注解类型
- **bidirectional**: 是否双向别名（默认true）
- **priority**: 别名优先级（默认0）

### 3. 实际应用示例

#### @LicenseImplementation 中的使用

```java
@Service
public @interface LicenseImplementation {
    
    @AliasFor(value = "scope", annotation = Service.class)
    Service.Scope scope() default Service.Scope.SINGLETON;
    
    @AliasFor(value = "value", annotation = Service.class)
    String serviceName() default "";
    
    @AliasFor(value = "interfaces", annotation = Service.class)
    Class<?>[] interfaces() default {};
}
```

#### @FallbackImplementation 中的使用

```java
@Service
public @interface FallbackImplementation {
    
    @AliasFor(value = "scope", annotation = Service.class)
    Service.Scope serviceScope() default Service.Scope.SINGLETON;
    
    @AliasFor(value = "value", annotation = Service.class)
    String serviceName() default "";
    
    @AliasFor(value = "interfaces", annotation = Service.class)
    Class<?>[] interfaces() default {};
}
```

## 解决的问题

### 1. 属性名称不统一

**之前的问题：**
```java
// @LicenseImplementation 使用 scope()
@LicenseImplementation(scope = Service.Scope.SINGLETON)

// @FallbackImplementation 使用 serviceScope()
@FallbackImplementation(serviceScope = Service.Scope.SINGLETON)
```

**现在的解决方案：**
```java
// 两者都通过@AliasFor映射到@Service.scope
// 在DependencyInjector中统一处理
Object scopeValue = AliasForUtils.getAliasedAttributeValue(annotation, Service.class, "scope");
```

### 2. 反射代码复杂

**之前的代码：**
```java
// 需要尝试多个可能的方法名
String[] scopeMethodNames = {"scope", "serviceScope", "lifecycle"};
for (String methodName : scopeMethodNames) {
    try {
        Method scopeMethod = annotationType.getMethod(methodName);
        // ...
    } catch (NoSuchMethodException e) {
        // 继续尝试
    }
}
```

**现在的代码：**
```java
// 统一通过@AliasFor机制获取
Object scopeValue = AliasForUtils.getAliasedAttributeValue(annotation, Service.class, "scope");
if (scopeValue instanceof Service.Scope) {
    return (Service.Scope) scopeValue == Service.Scope.SINGLETON;
}
```

## 工具类 AliasForUtils

### 主要方法

#### 1. getAliasedAttributeValue()
```java
// 获取通过@AliasFor映射的属性值
Object value = AliasForUtils.getAliasedAttributeValue(
    annotation,           // 注解实例
    Service.class,        // 目标注解类型
    "scope"              // 目标属性名称
);
```

#### 2. hasAliasFor()
```java
// 检查是否有指向目标的别名
boolean hasAlias = AliasForUtils.hasAliasFor(
    LicenseImplementation.class,  // 源注解类型
    Service.class,                // 目标注解类型
    "scope"                      // 目标属性名称
);
```

#### 3. getAttributeValue()
```java
// 获取注解属性值（支持别名解析）
Object value = AliasForUtils.getAttributeValue(annotation, "attributeName");
```

### 缓存机制

AliasForUtils 内置了两级缓存：

1. **别名信息缓存**: 缓存注解类型的别名关系
2. **属性值缓存**: 缓存注解实例的属性值

```java
// 获取缓存统计
Map<String, Object> stats = AliasForUtils.getCacheStatistics();
System.out.println("别名缓存大小: " + stats.get("aliasCacheSize"));
System.out.println("值缓存大小: " + stats.get("valueCacheSize"));

// 清理缓存
AliasForUtils.clearCache();
```

## DependencyInjector 中的集成

### 更新的 hasServiceSingletonScope 方法

```java
private boolean hasServiceSingletonScope(Class<?> clazz) {
    // 1. 直接检查@Service注解
    Service serviceAnnotation = clazz.getAnnotation(Service.class);
    if (serviceAnnotation != null) {
        return serviceAnnotation.scope() == Service.Scope.SINGLETON;
    }

    // 2. 检查组合注解（支持@AliasFor机制）
    for (Annotation annotation : clazz.getAnnotations()) {
        Class<? extends Annotation> annotationType = annotation.annotationType();
        Service metaService = annotationType.getAnnotation(Service.class);
        
        if (metaService != null) {
            // 优先使用@AliasFor机制
            Object scopeValue = AliasForUtils.getAliasedAttributeValue(
                annotation, Service.class, "scope");
            if (scopeValue instanceof Service.Scope) {
                return (Service.Scope) scopeValue == Service.Scope.SINGLETON;
            }
            
            // 回退到传统反射方式（向后兼容）
            Service.Scope scope = getScopeByReflection(annotation, annotationType);
            if (scope != null) {
                return scope == Service.Scope.SINGLETON;
            }
            
            // 使用元注解默认值
            return metaService.scope() == Service.Scope.SINGLETON;
        }
    }
    
    return false;
}
```

### 增强的日志输出

```java
logger.debug("是否应该作为单例: {}, @Singleton注解: {}, 延迟单例: {}, @Service单例scope: {}, 支持@AliasFor: {}",
    shouldBeSingleton,
    hasSingletonAnnotation(implementationClass),
    lazySingletonServices.contains(serviceClass),
    hasServiceSingletonScope(implementationClass),
    hasAliasForSupport(implementationClass));
```

## 最佳实践

### 1. 组合注解设计
- 使用@AliasFor统一属性名称
- 保持向后兼容性
- 提供清晰的文档说明

### 2. 性能考虑
- 利用AliasForUtils的缓存机制
- 避免重复的反射调用
- 在适当时候清理缓存

### 3. 错误处理
- 检查@AliasFor的配置是否正确
- 处理属性类型不匹配的情况
- 提供有意义的错误信息

## 迁移指南

### 从旧版本迁移

1. **保留现有属性**: 不要删除原有的属性方法
2. **添加@AliasFor**: 在新的或统一的属性上添加@AliasFor注解
3. **更新文档**: 说明推荐使用的属性名称
4. **渐进式迁移**: 逐步迁移到统一的属性名称

### 示例迁移

```java
// 旧版本
@FallbackImplementation(serviceScope = Service.Scope.SINGLETON)

// 新版本（推荐）
@FallbackImplementation(serviceScope = Service.Scope.SINGLETON)  // 通过@AliasFor映射

// 或者使用统一的属性名（如果将来支持）
@FallbackImplementation(scope = Service.Scope.SINGLETON)
```

## 总结

@AliasFor 机制为我们的依赖注入框架带来了：

- ✅ **统一性**: 统一了组合注解的属性名称
- ✅ **兼容性**: 保持了向后兼容
- ✅ **简洁性**: 简化了反射代码
- ✅ **性能**: 通过缓存提高了性能
- ✅ **扩展性**: 为未来的功能扩展奠定了基础

这个机制让我们的组合注解系统更加成熟和易用，为开发者提供了更好的使用体验。
