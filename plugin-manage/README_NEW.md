# 许可证管理框架 - 注解驱动版本

## 概述

基于 OSGi 服务和注解驱动的许可证业务注册机制，支持自动扫描和注册带注解的实现类。

## 核心特性

### 🎯 **注解驱动**
- `@LicenseImplementation` - 标识许可证实现
- `@FallbackImplementation` - 标识回退实现
- 自动扫描和注册

### 🔧 **OSGi 服务机制**
- `IPackageScanProvider` - 包扫描提供者服务
- 动态服务发现和跟踪
- ServiceLoader 兼容性支持

### 📦 **统一管理**
- `LicenseBusinessRegistry` - 统一注册器
- `DependencyInjector` - 依赖注入器（包含包扫描功能）
- 优先级和命名服务支持

## 使用方法

### 1. 添加注解到实现类

```java
// 回退实现
@FallbackImplementation(
    value = IFeishuAuthenticatorEnhancer.class,
    description = "飞书认证增强默认实现",
    priority = 100
)
public class FeishuDefaultAuthenticatorEnhancer implements IFeishuAuthenticatorEnhancer {
    // 实现代码
}

// 许可证实现
@LicenseImplementation(
    level = "PREMIUM", 
    description = "飞书认证增强功能", 
    premium = true
)
public class FeishuLicensedAuthenticatorEnhancer implements IFeishuAuthenticatorEnhancer {
    // 实现代码
}
```

### 2. 创建包扫描提供者

```java
public class FeishuPackageScanProvider implements IPackageScanProvider {
    @Override
    public String[] getScanPackages() {
        return new String[] { "com.fasnote.alm.auth.feishu" };
    }
    
    @Override
    public String getPluginId() {
        return "feishu-auth-plugin";
    }
    
    @Override
    public String getName() {
        return "Feishu Authentication Package Scanner";
    }
}
```

### 3. 注册 OSGi 服务

```java
public class Activator implements BundleActivator {
    private ServiceRegistration<IPackageScanProvider> registration;
    
    public void start(BundleContext context) throws Exception {
        registration = context.registerService(
            IPackageScanProvider.class,
            new FeishuPackageScanProvider(),
            null
        );
    }
    
    public void stop(BundleContext context) throws Exception {
        if (registration != null) {
            registration.unregister();
        }
    }
}
```

### 4. 添加 ServiceLoader 配置

创建文件：`META-INF/services/com.fasnote.alm.injection.api.IPackageScanProvider`
```
com.fasnote.alm.auth.feishu.FeishuPackageScanProvider
```

## 工作原理

1. **插件启动** → 注册 `IPackageScanProvider` 服务
2. **服务发现** → `DependencyInjector` 发现所有包扫描提供者
3. **收集包路径** → 收集所有插件声明的扫描包路径
4. **自动扫描** → `LicenseModule` 扫描收集到的包路径
5. **注解发现** → 发现带注解的实现类
6. **自动注册** → 注册到 DI 容器
7. **运行时选择** → 根据许可证状态选择实现

## 优先级规则

- **许可证实现**：默认优先级 50（高优先级）
- **回退实现**：默认优先级 100（低优先级）
- **数字越小优先级越高**

## 故障排除

### 常见问题

1. **类未被扫描到**
   - 检查 `IPackageScanProvider` 服务是否注册成功
   - 确认包路径是否正确
   - 确认注解是否正确添加
   - 检查 ServiceLoader 配置文件是否存在

2. **服务注入失败**
   - 检查接口和实现类的匹配
   - 确认 DI 容器配置正确

### 调试技巧

- 查看启动日志中的服务注册信息
- 使用验证器测试注册情况
- 检查包扫描管理器的提供者信息

## 最佳实践

1. **使用注解**：优先使用注解驱动的方式
2. **实现包扫描提供者**：每个插件实现自己的 `IPackageScanProvider`
3. **双重配置**：同时配置 OSGi 服务和 ServiceLoader
4. **合理设置优先级**：许可证实现使用较小的数字
5. **添加描述**：为注解添加有意义的描述
6. **测试验证**：确保许可证验证和回退机制正常工作
