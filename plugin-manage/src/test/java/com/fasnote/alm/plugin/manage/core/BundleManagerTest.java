package com.fasnote.alm.plugin.manage.core;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import java.util.Dictionary;
import java.util.Hashtable;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.osgi.framework.Bundle;
import org.osgi.framework.BundleContext;
import org.osgi.framework.wiring.BundleWiring;

@ExtendWith(MockitoExtension.class)
class BundleManagerTest {

    @Mock
    private BundleContext bundleContext;

    @Mock
    private Bundle bundle1;

    @Mock
    private Bundle bundle2;

    @Mock
    private BundleWiring bundleWiring;

    private BundleManager bundleManager;

    @BeforeEach
    void setUp() {
        bundleManager = new BundleManager(bundleContext);
    }

    @Test
    void testFindBundleByPluginId() {
        // Given
        String pluginId = "test-plugin";
        Bundle[] bundles = {bundle1, bundle2};
        
        when(bundleContext.getBundles()).thenReturn(bundles);
        when(bundle1.getSymbolicName()).thenReturn("other-plugin");
        when(bundle1.getState()).thenReturn(Bundle.ACTIVE);
        when(bundle2.getSymbolicName()).thenReturn(pluginId);
        when(bundle2.getState()).thenReturn(Bundle.ACTIVE);

        // When
        Bundle result = bundleManager.findBundleByPluginId(pluginId);

        // Then
        assertEquals(bundle2, result);
    }

    @Test
    void testFindBundleByPluginId_NotFound() {
        // Given
        String pluginId = "non-existing-plugin";
        Bundle[] bundles = {bundle1, bundle2};
        
        when(bundleContext.getBundles()).thenReturn(bundles);
        when(bundle1.getSymbolicName()).thenReturn("plugin1");
        when(bundle2.getSymbolicName()).thenReturn("plugin2");

        // When
        Bundle result = bundleManager.findBundleByPluginId(pluginId);

        // Then
        assertNull(result);
    }

    @Test
    void testFindBundleByPluginId_NullBundleContext() {
        // Given
        BundleManager managerWithNullContext = new BundleManager(null);
        String pluginId = "test-plugin";

        // When
        Bundle result = managerWithNullContext.findBundleByPluginId(pluginId);

        // Then
        assertNull(result);
    }

    @Test
    void testFindBundleBySymbolicName() {
        // Given
        String symbolicName = "test-bundle";
        Bundle[] bundles = {bundle1, bundle2};
        
        when(bundleContext.getBundles()).thenReturn(bundles);
        when(bundle1.getSymbolicName()).thenReturn("other-bundle");
        when(bundle1.getState()).thenReturn(Bundle.ACTIVE);
        when(bundle2.getSymbolicName()).thenReturn(symbolicName);
        when(bundle2.getState()).thenReturn(Bundle.ACTIVE);

        // When
        Bundle result = bundleManager.findBundleBySymbolicName(symbolicName);

        // Then
        assertEquals(bundle2, result);
    }

    @Test
    void testIsPluginRequiresLicense_WithBundle_True() {
        // Given
        String pluginId = "test-plugin";
        Dictionary<String, String> headers = new Hashtable<>();
        headers.put("ALM-License-Required", "true");
        
        when(bundle1.getHeaders()).thenReturn(headers);

        // When
        boolean result = bundleManager.isPluginRequiresLicense(bundle1, pluginId);

        // Then
        assertTrue(result);
    }

    @Test
    void testIsPluginRequiresLicense_WithBundle_False() {
        // Given
        String pluginId = "test-plugin";
        Dictionary<String, String> headers = new Hashtable<>();
        headers.put("ALM-License-Required", "false");
        
        when(bundle1.getHeaders()).thenReturn(headers);

        // When
        boolean result = bundleManager.isPluginRequiresLicense(bundle1, pluginId);

        // Then
        assertFalse(result);
    }

    @Test
    void testIsPluginRequiresLicense_WithBundle_NotSet() {
        // Given
        String pluginId = "test-plugin";
        Dictionary<String, String> headers = new Hashtable<>();
        
        when(bundle1.getHeaders()).thenReturn(headers);

        // When
        boolean result = bundleManager.isPluginRequiresLicense(bundle1, pluginId);

        // Then
        assertFalse(result);
    }

    @Test
    void testIsPluginRequiresLicense_WithBundle_Null() {
        // Given
        String pluginId = "test-plugin";

        // When
        boolean result = bundleManager.isPluginRequiresLicense(null, pluginId);

        // Then
        assertFalse(result);
    }

    @Test
    void testIsPluginRequiresLicense_WithPluginId() {
        // Given
        String pluginId = "test-plugin";
        Bundle[] bundles = {bundle1};
        Dictionary<String, String> headers = new Hashtable<>();
        headers.put("ALM-License-Required", "true");
        
        when(bundleContext.getBundles()).thenReturn(bundles);
        when(bundle1.getSymbolicName()).thenReturn(pluginId);
        when(bundle1.getState()).thenReturn(Bundle.ACTIVE);
        when(bundle1.getHeaders()).thenReturn(headers);

        // When
        boolean result = bundleManager.isPluginRequiresLicense(pluginId);

        // Then
        assertTrue(result);
    }

    @Test
    void testGetBundleStateName() {
        // Test all bundle states
        assertEquals("UNINSTALLED", bundleManager.getBundleStateName(Bundle.UNINSTALLED));
        assertEquals("INSTALLED", bundleManager.getBundleStateName(Bundle.INSTALLED));
        assertEquals("RESOLVED", bundleManager.getBundleStateName(Bundle.RESOLVED));
        assertEquals("STARTING", bundleManager.getBundleStateName(Bundle.STARTING));
        assertEquals("STOPPING", bundleManager.getBundleStateName(Bundle.STOPPING));
        assertEquals("ACTIVE", bundleManager.getBundleStateName(Bundle.ACTIVE));
        assertEquals("UNKNOWN(999)", bundleManager.getBundleStateName(999));
    }

    @Test
    void testGetBundleClassLoader_Success() {
        // Given
        ClassLoader expectedClassLoader = mock(ClassLoader.class);
        
        when(bundle1.adapt(BundleWiring.class)).thenReturn(bundleWiring);
        when(bundleWiring.getClassLoader()).thenReturn(expectedClassLoader);

        // When
        ClassLoader result = bundleManager.getBundleClassLoader(bundle1);

        // Then
        assertEquals(expectedClassLoader, result);
    }

    @Test
    void testGetBundleClassLoader_NullBundle() {
        // When
        ClassLoader result = bundleManager.getBundleClassLoader(null);

        // Then
        assertNull(result);
    }

    @Test
    void testGetBundleClassLoader_NullBundleWiring() {
        // Given
        when(bundle1.adapt(BundleWiring.class)).thenReturn(null);

        // When
        ClassLoader result = bundleManager.getBundleClassLoader(bundle1);

        // Then
        assertNull(result);
    }

    @Test
    void testIsBundleActive_True() {
        // Given
        when(bundle1.getState()).thenReturn(Bundle.ACTIVE);

        // When
        boolean result = bundleManager.isBundleActive(bundle1);

        // Then
        assertTrue(result);
    }

    @Test
    void testIsBundleActive_False() {
        // Given
        when(bundle1.getState()).thenReturn(Bundle.INSTALLED);

        // When
        boolean result = bundleManager.isBundleActive(bundle1);

        // Then
        assertFalse(result);
    }

    @Test
    void testIsBundleActive_NullBundle() {
        // When
        boolean result = bundleManager.isBundleActive(null);

        // Then
        assertFalse(result);
    }

    @Test
    void testGetAllBundleInfo() {
        // Given
        Bundle[] bundles = {bundle1, bundle2};
        
        when(bundleContext.getBundles()).thenReturn(bundles);
        when(bundle1.getSymbolicName()).thenReturn("bundle1");
        when(bundle1.getState()).thenReturn(Bundle.ACTIVE);
        when(bundle2.getSymbolicName()).thenReturn("bundle2");
        when(bundle2.getState()).thenReturn(Bundle.INSTALLED);

        // When
        Map<String, String> result = bundleManager.getAllBundleInfo();

        // Then
        assertEquals(2, result.size());
        assertEquals("ACTIVE", result.get("bundle1"));
        assertEquals("INSTALLED", result.get("bundle2"));
    }

    @Test
    void testBundleExists_True() {
        // Given
        String symbolicName = "existing-bundle";
        Bundle[] bundles = {bundle1};
        
        when(bundleContext.getBundles()).thenReturn(bundles);
        when(bundle1.getSymbolicName()).thenReturn(symbolicName);
        when(bundle1.getState()).thenReturn(Bundle.ACTIVE);

        // When
        boolean result = bundleManager.bundleExists(symbolicName);

        // Then
        assertTrue(result);
    }

    @Test
    void testBundleExists_False() {
        // Given
        String symbolicName = "non-existing-bundle";
        Bundle[] bundles = {bundle1};
        
        when(bundleContext.getBundles()).thenReturn(bundles);
        when(bundle1.getSymbolicName()).thenReturn("other-bundle");

        // When
        boolean result = bundleManager.bundleExists(symbolicName);

        // Then
        assertFalse(result);
    }

    @Test
    void testGetBundleCount() {
        // Given
        Bundle[] bundles = {bundle1, bundle2};
        when(bundleContext.getBundles()).thenReturn(bundles);

        // When
        int result = bundleManager.getBundleCount();

        // Then
        assertEquals(2, result);
    }

    @Test
    void testGetActiveBundleCount() {
        // Given
        Bundle[] bundles = {bundle1, bundle2};
        
        when(bundleContext.getBundles()).thenReturn(bundles);
        when(bundle1.getState()).thenReturn(Bundle.ACTIVE);
        when(bundle2.getState()).thenReturn(Bundle.INSTALLED);

        // When
        int result = bundleManager.getActiveBundleCount();

        // Then
        assertEquals(1, result);
    }
}
