package com.fasnote.alm.plugin.manage.integration;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.fasnote.alm.plugin.manage.model.PluginLicense;
import com.fasnote.alm.plugin.manage.model.ValidationResult;
import com.fasnote.alm.plugin.manage.repository.LicenseRepository;
import com.fasnote.alm.plugin.manage.repository.impl.InMemoryLicenseRepository;
import com.fasnote.alm.plugin.manage.security.SecurityValidator;
import com.fasnote.alm.plugin.manage.service.LicenseService;
import com.fasnote.alm.plugin.manage.service.impl.LicenseServiceImpl;
import com.fasnote.alm.plugin.manage.util.JsonUtil;

/**
 * 许可证管理集成测试 测试各个组件之间的协作
 */
@DisplayName("许可证管理集成测试")
class LicenseManagementIntegrationTest {

	// 测试常量
	private static final String TEST_PLUGIN_ID = "test-plugin";
	private static final String TEST_PRODUCT_NAME = "Test Product";
	private static final String TEST_VERSION = "1.0.0";
	private static final String TEST_LICENSE_TYPE = "STANDARD";
	private static final String TEST_ISSUER = "FasNote";
	private static final String TEST_LICENSED_TO = "Test User";
	private static final String TEST_ORGANIZATION = "Test Organization";
	private static final int TEST_MAX_USERS = 10;

	private LicenseRepository realRepository;
	private SecurityValidator realSecurityValidator;
	private LicenseService integrationLicenseService;

	/**
	 * 创建真实的安全验证器（简化版本用于测试）
	 */
	private SecurityValidator createRealSecurityValidator() {
		return new SecurityValidator(null) {
			@Override
			public ValidationResult validateLicense(PluginLicense license) {
				return null;
				
			}
		};
	}

	@BeforeEach
	void setUpIntegration() {
		// 使用真实的组件进行集成测试
		realRepository = new InMemoryLicenseRepository();
		realSecurityValidator = createRealSecurityValidator();

		integrationLicenseService = new LicenseServiceImpl(realRepository, realSecurityValidator);
	}

	@Test
	@DisplayName("并发访问应该是安全的")
	void shouldBeConcurrentSafe() throws InterruptedException {
		// Given
		PluginLicense license = createTestLicense();
		realRepository.save(license);

		// When - 并发访问
		int threadCount = 10;
		Thread[] threads = new Thread[threadCount];
		boolean[] results = new boolean[threadCount];

		for (int i = 0; i < threadCount; i++) {
			final int index = i;
			threads[i] = new Thread(() -> {
				results[index] = integrationLicenseService.hasValidLicense(TEST_PLUGIN_ID);
			});
		}

		// 启动所有线程
		for (Thread thread : threads) {
			thread.start();
		}

		// 等待所有线程完成
		for (Thread thread : threads) {
			thread.join();
		}

		// Then - 所有结果应该一致
		for (boolean result : results) {
			assertTrue(result);
		}
	}

	@Test
	@DisplayName("过期许可证应该验证失败")
	void shouldFailValidationForExpiredLicense() {
		// Given
		PluginLicense expiredLicense = createExpiredTestLicense();
		realRepository.save(expiredLicense);

		// When
		ValidationResult result = integrationLicenseService.validatePluginLicense(TEST_PLUGIN_ID);

		// Then
		assertFalse(result.isValid());
		assertTrue(result.getMessage().contains("已过期"));
	}

	@Test
	@DisplayName("未生效许可证应该验证失败")
	void shouldFailValidationForFutureLicense() {
		// Given
		PluginLicense futureLicense = createFutureTestLicense();
		realRepository.save(futureLicense);

		// When
		ValidationResult result = integrationLicenseService.validatePluginLicense(TEST_PLUGIN_ID);

		// Then
		assertFalse(result.isValid());
		assertTrue(result.getMessage().contains("尚未生效"));
	}

	@Test
	@DisplayName("完整的许可证生命周期应该正常工作")
	void shouldHandleCompleteLicenseLifecycle() {
		// 1. 创建并保存许可证
		PluginLicense license = createTestLicense();
		realRepository.save(license);

		// 2. 验证许可证存在
		assertTrue(realRepository.existsByPluginId(TEST_PLUGIN_ID));

		// 3. 获取许可证
		Optional<PluginLicense> retrievedLicense = integrationLicenseService.getLicense(TEST_PLUGIN_ID);
		assertTrue(retrievedLicense.isPresent());
		assertEquals(TEST_PLUGIN_ID, retrievedLicense.get().getPluginId());

		// 4. 验证许可证
		ValidationResult validationResult = integrationLicenseService.validateLicense(retrievedLicense.get());
		assertTrue(validationResult.isValid());

		// 5. 检查许可证有效性
		assertTrue(integrationLicenseService.hasValidLicense(TEST_PLUGIN_ID));

		// 6. 删除许可证
		realRepository.deleteByPluginId(TEST_PLUGIN_ID);
		assertFalse(realRepository.existsByPluginId(TEST_PLUGIN_ID));
	}

	@Test
	@DisplayName("多个许可证应该能够独立管理")
	void shouldManageMultipleLicensesIndependently() {
		// Given
		PluginLicense license1 = createTestLicense("plugin1", LocalDateTime.now().plusDays(30));
		PluginLicense license2 = createTestLicense("plugin2", LocalDateTime.now().minusDays(1)); // 过期
		PluginLicense license3 = createTestLicense("plugin3", LocalDateTime.now().plusDays(60));

		realRepository.save(license1);
		realRepository.save(license2);
		realRepository.save(license3);

		// When & Then
		assertTrue(integrationLicenseService.hasValidLicense("plugin1"));
		assertFalse(integrationLicenseService.hasValidLicense("plugin2")); // 过期
		assertTrue(integrationLicenseService.hasValidLicense("plugin3"));

		// 验证各自的状态
		ValidationResult result1 = integrationLicenseService.validatePluginLicense("plugin1");
		ValidationResult result2 = integrationLicenseService.validatePluginLicense("plugin2");
		ValidationResult result3 = integrationLicenseService.validatePluginLicense("plugin3");

		assertTrue(result1.isValid());
		assertFalse(result2.isValid());
		assertTrue(result3.isValid());
	}

	@Test
	@DisplayName("刷新许可证状态应该正常工作")
	void shouldRefreshLicenseStatusCorrectly() {
		// Given
		PluginLicense validLicense = createTestLicense("valid-plugin", LocalDateTime.now().plusDays(30));
		PluginLicense expiredLicense = createTestLicense("expired-plugin", LocalDateTime.now().minusDays(1));

		realRepository.save(validLicense);
		realRepository.save(expiredLicense);

		// When
		assertDoesNotThrow(() -> integrationLicenseService.refreshLicenseStatus());

		// Then - 验证状态仍然正确
		assertTrue(integrationLicenseService.hasValidLicense("valid-plugin"));
		assertFalse(integrationLicenseService.hasValidLicense("expired-plugin"));
	}

	@Test
	@DisplayName("许可证更新应该正常工作")
	void shouldUpdateLicenseCorrectly() {
		// Given - 创建原始许可证
		PluginLicense originalLicense = createTestLicense(TEST_PLUGIN_ID, LocalDateTime.now().plusDays(30));
		realRepository.save(originalLicense);

		// 验证原始许可证
		assertTrue(integrationLicenseService.hasValidLicense(TEST_PLUGIN_ID));

		// When - 更新为过期许可证
		PluginLicense updatedLicense = createTestLicense(TEST_PLUGIN_ID, LocalDateTime.now().minusDays(1));
		realRepository.save(updatedLicense); // 覆盖原有许可证

		// Then - 验证更新后的状态
		assertFalse(integrationLicenseService.hasValidLicense(TEST_PLUGIN_ID));

		ValidationResult result = integrationLicenseService.validatePluginLicense(TEST_PLUGIN_ID);
		assertFalse(result.isValid());
		assertTrue(result.getMessage().contains("已过期"));
	}

	// 辅助方法

	/**
	 * 创建测试用的许可证对象
	 */
	private PluginLicense createTestLicense() {
		return createTestLicense(TEST_PLUGIN_ID, LocalDateTime.now().plusDays(30));
	}

	/**
	 * 创建过期的测试许可证
	 */
	private PluginLicense createExpiredTestLicense() {
		return createTestLicense(TEST_PLUGIN_ID, LocalDateTime.now().minusDays(1));
	}

	/**
	 * 创建未来生效的测试许可证
	 */
	private PluginLicense createFutureTestLicense() {
		return createTestLicense(TEST_PLUGIN_ID, LocalDateTime.now().plusDays(1));
	}

	/**
	 * 创建测试用的许可证对象
	 */
	private PluginLicense createTestLicense(String pluginId, LocalDateTime expiryDate) {
		String licenseJson = createTestLicenseJson(pluginId, expiryDate);
		return new PluginLicense(pluginId, licenseJson);
	}

	/**
	 * 创建测试用的许可证数据（JSON格式）
	 */
	private String createTestLicenseJson(String pluginId, LocalDateTime expiryDate) {
		Map<String, Object> licenseData = new HashMap<>();

		// 基本信息
		licenseData.put("pluginId", pluginId);
		licenseData.put("productName", TEST_PRODUCT_NAME);
		licenseData.put("version", TEST_VERSION);
		licenseData.put("licenseType", TEST_LICENSE_TYPE);
		licenseData.put("issuer", TEST_ISSUER);

		// 时间信息
		licenseData.put("issueDate", LocalDateTime.now().toString());
		if (expiryDate != null) {
			licenseData.put("expiryDate", expiryDate.toString());
		}

		// 用户信息
		licenseData.put("licensedTo", TEST_LICENSED_TO);
		licenseData.put("organization", TEST_ORGANIZATION);
		licenseData.put("maxUsers", TEST_MAX_USERS);

		// 功能信息
		Map<String, Object> features = new HashMap<>();
		features.put("BasicFeature", true);
		features.put("AdvancedFeature", true);
		features.put("PremiumFeature", false);
		licenseData.put("features", features);

		// 限制信息
		Map<String, Object> limitations = new HashMap<>();
		limitations.put("maxProjects", 100);
		limitations.put("maxStorage", "10GB");
		licenseData.put("limitations", limitations);

		// 安全信息
		licenseData.put("machineCode", "TEST-MACHINE-CODE");
		licenseData.put("signature", "TEST-SIGNATURE");
		licenseData.put("contentHash", "TEST-HASH");

		return JsonUtil.toJson(licenseData);
	}
}