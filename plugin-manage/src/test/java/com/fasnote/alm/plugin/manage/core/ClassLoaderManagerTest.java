package com.fasnote.alm.plugin.manage.core;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.osgi.framework.Bundle;

import com.fasnote.alm.plugin.manage.api.IBundleManager;
import com.fasnote.alm.plugin.manage.classloader.EncryptedClassLoader;
import com.fasnote.alm.plugin.manage.model.PluginLicense;

@ExtendWith(MockitoExtension.class)
class ClassLoaderManagerTest {

    @Mock
    private IBundleManager bundleManager;

    @Mock
    private UnifiedLicenseProcessor licenseProcessor;

    @Mock
    private PluginLicense pluginLicense;

    @Mock
    private LicenseCache licenseCache;

    @Mock
    private Bundle bundle;

    @Mock
    private ClassLoader bundleClassLoader;

    private Map<String, PluginLicense> pluginLicenses;
    private ClassLoaderManager classLoaderManager;

    @BeforeEach
    void setUp() {
        pluginLicenses = new ConcurrentHashMap<>();
        classLoaderManager = new ClassLoaderManager(pluginLicenses, bundleManager, licenseProcessor);
    }

    @Test
    void testCreateEncryptedClassLoaderForPlugin_NoLicense() {
        // Given
        String pluginId = "test-plugin";

        // When
        Optional<EncryptedClassLoader> result = classLoaderManager.createEncryptedClassLoaderForPlugin(pluginId);

        // Then
        assertFalse(result.isPresent());
    }

    @Test
    void testCreateEncryptedClassLoaderForPlugin_NoEncryptedClasses() {
        // Given
        String pluginId = "test-plugin";
        pluginLicenses.put(pluginId, pluginLicense);
        
        when(pluginLicense.hasEncryptedClasses()).thenReturn(false);

        // When
        Optional<EncryptedClassLoader> result = classLoaderManager.createEncryptedClassLoaderForPlugin(pluginId);

        // Then
        assertFalse(result.isPresent());
    }

    @Test
    void testCreateEncryptedClassLoaderForPlugin_WithCache() {
        // Given
        String pluginId = "test-plugin";
        pluginLicenses.put(pluginId, pluginLicense);
        
        Map<String, byte[]> decryptedClasses = new HashMap<>();
        decryptedClasses.put("com.test.TestClass", new byte[]{1, 2, 3});
        
        when(pluginLicense.hasEncryptedClasses()).thenReturn(true);
        when(licenseProcessor.getCachedLicense(pluginId)).thenReturn(licenseCache);
        when(licenseCache.hasEncryptedClasses()).thenReturn(true);
        when(licenseCache.getDecryptedClasses()).thenReturn(decryptedClasses);
        when(bundleManager.findBundleByPluginId(pluginId)).thenReturn(bundle);
        when(bundleManager.getBundleClassLoader(bundle)).thenReturn(bundleClassLoader);

        // When
        Optional<EncryptedClassLoader> result = classLoaderManager.createEncryptedClassLoaderForPlugin(pluginId);

        // Then
        assertTrue(result.isPresent());
        verify(licenseProcessor).getCachedLicense(pluginId);
        verify(bundleManager).findBundleByPluginId(pluginId);
    }

    @Test
    void testCreateEncryptedClassLoaderForPlugin_WithoutCache() {
        // Given
        String pluginId = "test-plugin";
        String licenseFilePath = "/path/to/license.lic";
        pluginLicenses.put(pluginId, pluginLicense);
        
        when(pluginLicense.hasEncryptedClasses()).thenReturn(true);
        when(licenseProcessor.getCachedLicense(pluginId)).thenReturn(null);
        when(pluginLicense.getLicenseFilePath()).thenReturn(licenseFilePath);
        when(bundleManager.findBundleByPluginId(pluginId)).thenReturn(bundle);
        when(bundleManager.getBundleClassLoader(bundle)).thenReturn(bundleClassLoader);

        // When
        Optional<EncryptedClassLoader> result = classLoaderManager.createEncryptedClassLoaderForPlugin(pluginId);

        // Then
        // 由于decryptLicenseFile方法会抛出异常（因为是mock环境），所以应该返回empty
        assertFalse(result.isPresent());
    }

    @Test
    void testGetEncryptedClassLoader_Existing() {
        // Given
        String pluginId = "test-plugin";
        pluginLicenses.put(pluginId, pluginLicense);
        
        Map<String, byte[]> decryptedClasses = new HashMap<>();
        decryptedClasses.put("com.test.TestClass", new byte[]{1, 2, 3});
        
        when(pluginLicense.hasEncryptedClasses()).thenReturn(true);
        when(licenseProcessor.getCachedLicense(pluginId)).thenReturn(licenseCache);
        when(licenseCache.hasEncryptedClasses()).thenReturn(true);
        when(licenseCache.getDecryptedClasses()).thenReturn(decryptedClasses);
        when(bundleManager.findBundleByPluginId(pluginId)).thenReturn(bundle);
        when(bundleManager.getBundleClassLoader(bundle)).thenReturn(bundleClassLoader);

        // 先创建一个类加载器
        classLoaderManager.createEncryptedClassLoaderForPlugin(pluginId);

        // When
        Optional<EncryptedClassLoader> result = classLoaderManager.getEncryptedClassLoader(pluginId);

        // Then
        assertTrue(result.isPresent());
    }

    @Test
    void testGetEncryptedClassLoader_NonExisting() {
        // Given
        String pluginId = "non-existing-plugin";

        // When
        Optional<EncryptedClassLoader> result = classLoaderManager.getEncryptedClassLoader(pluginId);

        // Then
        assertFalse(result.isPresent());
    }

    @Test
    void testGetEncryptedClassLoader_EmptyPluginId() {
        // When
        Optional<EncryptedClassLoader> result = classLoaderManager.getEncryptedClassLoader("");

        // Then
        assertFalse(result.isPresent());
    }

    @Test
    void testGetEncryptedClassLoader_NullPluginId() {
        // When
        Optional<EncryptedClassLoader> result = classLoaderManager.getEncryptedClassLoader(null);

        // Then
        assertFalse(result.isPresent());
    }

    @Test
    void testGetBusinessPluginClassLoader_Success() {
        // Given
        String pluginId = "test-plugin";
        
        when(bundleManager.findBundleByPluginId(pluginId)).thenReturn(bundle);
        when(bundleManager.getBundleClassLoader(bundle)).thenReturn(bundleClassLoader);

        // When
        ClassLoader result = classLoaderManager.getBusinessPluginClassLoader(pluginId);

        // Then
        assertEquals(bundleClassLoader, result);
        verify(bundleManager).findBundleByPluginId(pluginId);
        verify(bundleManager).getBundleClassLoader(bundle);
    }

    @Test
    void testGetBusinessPluginClassLoader_BundleNotFound() {
        // Given
        String pluginId = "non-existing-plugin";
        
        when(bundleManager.findBundleByPluginId(pluginId)).thenReturn(null);

        // When
        ClassLoader result = classLoaderManager.getBusinessPluginClassLoader(pluginId);

        // Then
        assertEquals(ClassLoaderManager.class.getClassLoader(), result);
    }

    @Test
    void testGetBusinessPluginClassLoader_ClassLoaderNotFound() {
        // Given
        String pluginId = "test-plugin";
        
        when(bundleManager.findBundleByPluginId(pluginId)).thenReturn(bundle);
        when(bundleManager.getBundleClassLoader(bundle)).thenReturn(null);

        // When
        ClassLoader result = classLoaderManager.getBusinessPluginClassLoader(pluginId);

        // Then
        assertEquals(ClassLoaderManager.class.getClassLoader(), result);
    }

    @Test
    void testCleanupClassLoader() {
        // Given
        String pluginId = "test-plugin";
        pluginLicenses.put(pluginId, pluginLicense);
        
        Map<String, byte[]> decryptedClasses = new HashMap<>();
        decryptedClasses.put("com.test.TestClass", new byte[]{1, 2, 3});
        
        when(pluginLicense.hasEncryptedClasses()).thenReturn(true);
        when(licenseProcessor.getCachedLicense(pluginId)).thenReturn(licenseCache);
        when(licenseCache.hasEncryptedClasses()).thenReturn(true);
        when(licenseCache.getDecryptedClasses()).thenReturn(decryptedClasses);
        when(bundleManager.findBundleByPluginId(pluginId)).thenReturn(bundle);
        when(bundleManager.getBundleClassLoader(bundle)).thenReturn(bundleClassLoader);

        // 先创建一个类加载器
        classLoaderManager.createEncryptedClassLoaderForPlugin(pluginId);
        assertTrue(classLoaderManager.getEncryptedClassLoader(pluginId).isPresent());

        // When
        classLoaderManager.cleanupClassLoader(pluginId);

        // Then
        assertFalse(classLoaderManager.getEncryptedClassLoader(pluginId).isPresent());
    }

    @Test
    void testCleanupClassLoader_NonExisting() {
        // Given
        String pluginId = "non-existing-plugin";

        // When & Then (should not throw exception)
        assertDoesNotThrow(() -> classLoaderManager.cleanupClassLoader(pluginId));
    }

    @Test
    void testCleanupAllClassLoaders() {
        // Given
        String pluginId1 = "plugin1";
        String pluginId2 = "plugin2";
        pluginLicenses.put(pluginId1, pluginLicense);
        pluginLicenses.put(pluginId2, pluginLicense);
        
        Map<String, byte[]> decryptedClasses = new HashMap<>();
        decryptedClasses.put("com.test.TestClass", new byte[]{1, 2, 3});
        
        when(pluginLicense.hasEncryptedClasses()).thenReturn(true);
        when(licenseProcessor.getCachedLicense(any())).thenReturn(licenseCache);
        when(licenseCache.hasEncryptedClasses()).thenReturn(true);
        when(licenseCache.getDecryptedClasses()).thenReturn(decryptedClasses);
        when(bundleManager.findBundleByPluginId(any())).thenReturn(bundle);
        when(bundleManager.getBundleClassLoader(bundle)).thenReturn(bundleClassLoader);

        // 创建两个类加载器
        classLoaderManager.createEncryptedClassLoaderForPlugin(pluginId1);
        classLoaderManager.createEncryptedClassLoaderForPlugin(pluginId2);
        assertEquals(2, classLoaderManager.getClassLoaderCount());

        // When
        classLoaderManager.cleanupAllClassLoaders();

        // Then
        assertEquals(0, classLoaderManager.getClassLoaderCount());
    }

    @Test
    void testGetClassLoaderCount() {
        // Given
        assertEquals(0, classLoaderManager.getClassLoaderCount());

        String pluginId = "test-plugin";
        pluginLicenses.put(pluginId, pluginLicense);
        
        Map<String, byte[]> decryptedClasses = new HashMap<>();
        decryptedClasses.put("com.test.TestClass", new byte[]{1, 2, 3});
        
        when(pluginLicense.hasEncryptedClasses()).thenReturn(true);
        when(licenseProcessor.getCachedLicense(pluginId)).thenReturn(licenseCache);
        when(licenseCache.hasEncryptedClasses()).thenReturn(true);
        when(licenseCache.getDecryptedClasses()).thenReturn(decryptedClasses);
        when(bundleManager.findBundleByPluginId(pluginId)).thenReturn(bundle);
        when(bundleManager.getBundleClassLoader(bundle)).thenReturn(bundleClassLoader);

        // When
        classLoaderManager.createEncryptedClassLoaderForPlugin(pluginId);

        // Then
        assertEquals(1, classLoaderManager.getClassLoaderCount());
    }
}
