package com.fasnote.alm.plugin.manage.injection.module;

import com.fasnote.alm.injection.api.IPackageScanProvider;

/**
 * 许可证管理模块包扫描提供者
 * 
 * 通过 OSGi 服务机制声明许可证管理模块需要扫描的包路径
 */
public class LicensePackageScanProvider implements IPackageScanProvider {
    
    @Override
    public String[] getScanPackages() {
        return new String[] {
            "com.fasnote.alm.plugin.manage.injection.module"
        };
    }
    
    @Override
    public String getPluginId() {
        return "license-manage-plugin";
    }
    
    @Override
    public String getName() {
        return "License Management Package Scanner";
    }
    
    @Override
    public int getPriority() {
        return 1; // 最高优先级，确保许可证模块最先被扫描和加载
    }
}
