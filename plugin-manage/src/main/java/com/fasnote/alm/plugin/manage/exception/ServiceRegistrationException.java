package com.fasnote.alm.plugin.manage.exception;

/**
 * 服务注册失败异常
 * 当服务注册过程中发生错误时抛出此异常
 */
public class ServiceRegistrationException extends LicenseException {

    private static final long serialVersionUID = 1L;

    /**
     * 构造函数
     *
     * @param message  异常消息
     * @param pluginId 插件ID
     */
    public ServiceRegistrationException(String message, String pluginId) {
        super(message, "SERVICE_REGISTRATION_FAILED", pluginId);
    }

    /**
     * 构造函数
     *
     * @param message  异常消息
     * @param pluginId 插件ID
     * @param cause    原因异常
     */
    public ServiceRegistrationException(String message, String pluginId, Throwable cause) {
        super(message, "SERVICE_REGISTRATION_FAILED", cause);
        setParameters(pluginId);
    }

    /**
     * 构造函数
     *
     * @param message           异常消息
     * @param pluginId          插件ID
     * @param serviceInterface  服务接口
     * @param implementationName 实现类名称
     */
    public ServiceRegistrationException(String message, String pluginId, String serviceInterface, String implementationName) {
        super(message, "SERVICE_REGISTRATION_FAILED", pluginId, serviceInterface, implementationName);
    }

    /**
     * 构造函数
     *
     * @param message           异常消息
     * @param pluginId          插件ID
     * @param serviceInterface  服务接口
     * @param implementationName 实现类名称
     * @param cause             原因异常
     */
    public ServiceRegistrationException(String message, String pluginId, String serviceInterface, String implementationName, Throwable cause) {
        super(message, "SERVICE_REGISTRATION_FAILED", cause);
        setParameters(pluginId, serviceInterface, implementationName);
    }

    /**
     * 创建服务实例创建失败异常
     *
     * @param pluginId          插件ID
     * @param implementationName 实现类名称
     * @param cause             原因异常
     * @return 异常实例
     */
    public static ServiceRegistrationException instanceCreationFailed(String pluginId, String implementationName, Throwable cause) {
        return new ServiceRegistrationException(
            String.format("创建服务实例失败: %s", implementationName),
            pluginId, null, implementationName, cause
        );
    }

    /**
     * 创建服务接口不匹配异常
     *
     * @param pluginId          插件ID
     * @param serviceInterface  服务接口
     * @param implementationName 实现类名称
     * @return 异常实例
     */
    public static ServiceRegistrationException interfaceMismatch(String pluginId, String serviceInterface, String implementationName) {
        return new ServiceRegistrationException(
            String.format("实现类 %s 未实现接口 %s", implementationName, serviceInterface),
            pluginId, serviceInterface, implementationName
        );
    }

    /**
     * 创建类加载失败异常
     *
     * @param pluginId          插件ID
     * @param implementationName 实现类名称
     * @param cause             原因异常
     * @return 异常实例
     */
    public static ServiceRegistrationException classLoadFailed(String pluginId, String implementationName, Throwable cause) {
        return new ServiceRegistrationException(
            String.format("加载实现类失败: %s", implementationName),
            pluginId, null, implementationName, cause
        );
    }

    /**
     * 创建服务映射不存在异常
     *
     * @param pluginId 插件ID
     * @return 异常实例
     */
    public static ServiceRegistrationException noServiceMappings(String pluginId) {
        return new ServiceRegistrationException("许可证中未定义服务映射", pluginId);
    }

    /**
     * 创建运行时环境未初始化异常
     *
     * @param pluginId 插件ID
     * @return 异常实例
     */
    public static ServiceRegistrationException runtimeEnvironmentNotInitialized(String pluginId) {
        return new ServiceRegistrationException("插件运行时环境未初始化", pluginId);
    }
}
