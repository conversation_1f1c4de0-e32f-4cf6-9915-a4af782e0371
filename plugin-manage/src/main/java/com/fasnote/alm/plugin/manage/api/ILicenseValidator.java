package com.fasnote.alm.plugin.manage.api;

import com.fasnote.alm.plugin.manage.model.PluginLicense;
import com.fasnote.alm.plugin.manage.model.ValidationResult;
import com.fasnote.alm.plugin.manage.security.SecurityValidator;

/**
 * 许可证验证器接口
 * 负责许可证验证逻辑
 */
public interface ILicenseValidator {

    /**
     * 验证许可证
     *
     * @param license 许可证对象
     * @return 验证结果
     */
    ValidationResult validateLicense(PluginLicense license);

    /**
     * 检查插件是否有有效许可证
     *
     * @param pluginId 插件ID
     * @return 是否有效
     */
    boolean hasValidLicense(String pluginId);



    /**
     * 检查验证器是否已初始化
     *
     * @return 是否已初始化
     */
    boolean isInitialized();

    /**
     * 设置安全验证器
     *
     * @param securityValidator 安全验证器
     */
    void setSecurityValidator(SecurityValidator securityValidator);
}
