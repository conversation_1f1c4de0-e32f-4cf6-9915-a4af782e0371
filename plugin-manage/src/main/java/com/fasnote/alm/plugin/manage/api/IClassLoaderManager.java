package com.fasnote.alm.plugin.manage.api;

import java.util.Optional;

import com.fasnote.alm.plugin.manage.classloader.EncryptedClassLoader;

/**
 * 类加载器管理器接口
 * 负责加密类加载器的创建和管理
 */
public interface IClassLoaderManager {

    /**
     * 为指定插件创建加密类加载器
     *
     * @param pluginId 插件ID
     * @return 加密类加载器的Optional包装，如果无加密类则返回empty
     */
    Optional<EncryptedClassLoader> createEncryptedClassLoaderForPlugin(String pluginId);

    /**
     * 获取插件的加密类加载器
     *
     * @param pluginId 插件ID
     * @return 加密类加载器的Optional包装，如果不存在则返回empty
     */
    Optional<EncryptedClassLoader> getEncryptedClassLoader(String pluginId);

    /**
     * 获取业务插件Bundle的ClassLoader
     *
     * @param pluginId 插件ID
     * @return 业务插件Bundle的ClassLoader，如果未找到则返回当前ClassLoader
     */
    ClassLoader getBusinessPluginClassLoader(String pluginId);

    /**
     * 清理指定插件的类加载器
     *
     * @param pluginId 插件ID
     */
    void cleanupClassLoader(String pluginId);

    /**
     * 清理所有类加载器
     */
    void cleanupAllClassLoaders();

    /**
     * 获取已管理的类加载器数量
     *
     * @return 类加载器数量
     */
    int getClassLoaderCount();
}
