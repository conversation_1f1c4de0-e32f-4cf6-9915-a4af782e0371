package com.fasnote.alm.plugin.manage.api;

import java.util.Map;

import com.fasnote.alm.plugin.manage.model.ValidationResult;

/**
 * 许可证文件管理器接口
 * 负责许可证文件的扫描、加载、预加载功能
 */
public interface ILicenseFileManager {

    /**
     * 自动扫描并预加载许可证文件（仅解密验证，不加载实现类）
     */
    void autoScanAndLoadLicenses();

    /**
     * 从文件加载并激活许可证
     *
     * @param pluginId 插件ID
     * @return 激活结果
     */
    ValidationResult loadAndActivateLicenseFromFile(String pluginId);

    /**
     * 注册插件许可证（完整注册，包括加载实现类）
     *
     * @param pluginId    插件ID
     * @param licenseData 许可证数据（加密）
     * @return 验证结果
     */
    ValidationResult registerPluginLicense(String pluginId, byte[] licenseData);

    /**
     * 预加载插件许可证（仅解密验证，不加载实现类）
     *
     * @param pluginId    插件ID
     * @param licenseData 许可证数据（加密）
     * @return 注册结果
     */
    ValidationResult preloadPluginLicense(String pluginId, byte[] licenseData);

    /**
     * 从文件预加载插件许可证（仅解密验证，不加载实现类）
     *
     * @param pluginId        插件ID
     * @param licenseFilePath 许可证文件路径
     * @return 注册结果
     */
    ValidationResult preloadPluginLicenseFromFile(String pluginId, String licenseFilePath);

    /**
     * 重新扫描并加载新的许可证文件
     *
     * @return 新加载的许可证数量
     */
    int rescanLicenseFiles();

    /**
     * 扫描许可证文件
     *
     * @return 插件ID到许可证文件路径的映射
     */
    Map<String, String> scanLicenseFiles();
}
