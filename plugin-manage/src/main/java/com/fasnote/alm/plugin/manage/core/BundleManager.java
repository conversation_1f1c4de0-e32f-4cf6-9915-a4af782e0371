package com.fasnote.alm.plugin.manage.core;

import javax.inject.Inject;

import org.osgi.framework.Bundle;
import org.osgi.framework.BundleContext;
import org.osgi.framework.wiring.BundleWiring;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.plugin.manage.api.IBundleManager;

/**
 * Bundle管理器实现
 * 负责OSGi Bundle相关操作
 */
public class BundleManager implements IBundleManager {

    private static final Logger logger = LoggerFactory.getLogger(BundleManager.class);

    // OSGi Bundle上下文
    private final BundleContext bundleContext;

    /**
     * 构造函数（使用依赖注入）
     *
     * @param bundleContext OSGi Bundle上下文
     */
    @Inject
    public BundleManager(BundleContext bundleContext) {
        this.bundleContext = bundleContext;
    }

    @Override
    public Bundle findBundleByPluginId(String pluginId) {
        // 查找对应的Bundle
        return findBundleBySymbolicName(pluginId);
    }

    @Override
    public Bundle findBundleBySymbolicName(String symbolicName) {
        if (bundleContext == null) {
            logger.warn("BundleContext为空，无法查找Bundle: {}", symbolicName);
            return null;
        }

        if (symbolicName == null || symbolicName.trim().isEmpty()) {
            logger.warn("Bundle符号名称为空");
            return null;
        }

        Bundle[] bundles = bundleContext.getBundles();
        logger.debug("查找Bundle: {}，当前共有 {} 个Bundle", symbolicName, bundles.length);

        // 先列出所有相关的Bundle（用于调试）
        for (Bundle bundle : bundles) {
            String bundleName = bundle.getSymbolicName();
            if (bundleName != null && (bundleName.contains("fasnote") || bundleName.contains("feishu"))) {
                logger.debug("相关Bundle: {} (状态: {})", bundleName, getBundleStateName(bundle.getState()));
            }
        }

        for (Bundle bundle : bundles) {
            if (symbolicName.equals(bundle.getSymbolicName())) {
                // 检查Bundle状态 - 允许STARTING状态，因为ClassLoader通常已经可用
                if (bundle.getState() == Bundle.ACTIVE || bundle.getState() == Bundle.RESOLVED
                        || bundle.getState() == Bundle.STARTING) {
                    logger.info("找到Bundle: {} (状态: {})", symbolicName, getBundleStateName(bundle.getState()));
                    return bundle;
                } else {
                    logger.warn("Bundle状态不正确: {} (状态: {})", symbolicName, getBundleStateName(bundle.getState()));
                    return bundle; // 即使状态不理想，也尝试使用它
                }
            }
        }

        logger.warn("未找到Bundle: {}", symbolicName);
        return null;
    }

    @Override
    public boolean isPluginRequiresLicense(Bundle bundle, String pluginId) {
        if (bundle == null) {
            logger.debug("Bundle为空，默认不需要许可证验证: {}", pluginId);
            return false;
        }

        try {
            String licenseRequired = bundle.getHeaders().get("ALM-License-Required");
            if ("true".equalsIgnoreCase(licenseRequired)) {
                logger.debug("Bundle MANIFEST.MF中标记需要许可证: {}", pluginId);
                return true;
            } else {
                logger.debug("Bundle MANIFEST.MF中标记不需要许可证或未设置: {}", pluginId);
                return false;
            }
        } catch (Exception e) {
            logger.warn("检查Bundle MANIFEST.MF属性时发生异常: {}", pluginId, e);
            return false;
        }
    }

    @Override
    public boolean isPluginRequiresLicense(String pluginId) {
        if (pluginId == null || pluginId.trim().isEmpty()) {
            return false;
        }

        try {
            Bundle bundle = findBundleByPluginId(pluginId);
            return isPluginRequiresLicense(bundle, pluginId);
        } catch (Exception e) {
            logger.warn("检查Bundle MANIFEST.MF属性时发生异常: {}", pluginId, e);
        }

        // 如果无法获取Bundle信息，默认不需要许可证验证
        logger.debug("无法获取Bundle信息，默认不需要许可证验证: {}", pluginId);
        return false;
    }

    @Override
    public String getBundleStateName(int state) {
        switch (state) {
        case Bundle.UNINSTALLED:
            return "UNINSTALLED";
        case Bundle.INSTALLED:
            return "INSTALLED";
        case Bundle.RESOLVED:
            return "RESOLVED";
        case Bundle.STARTING:
            return "STARTING";
        case Bundle.STOPPING:
            return "STOPPING";
        case Bundle.ACTIVE:
            return "ACTIVE";
        default:
            return "UNKNOWN(" + state + ")";
        }
    }

    @Override
    public ClassLoader getBundleClassLoader(Bundle bundle) {
        if (bundle == null) {
            logger.warn("Bundle为空，无法获取ClassLoader");
            return null;
        }

        try {
            // 获取Bundle的ClassLoader
            BundleWiring bundleWiring = bundle.adapt(BundleWiring.class);
            if (bundleWiring != null) {
                ClassLoader bundleClassLoader = bundleWiring.getClassLoader();
                logger.debug("成功获取Bundle ClassLoader: {}", bundle.getSymbolicName());
                return bundleClassLoader;
            } else {
                logger.warn("无法获取Bundle的BundleWiring: {}", bundle.getSymbolicName());
                return null;
            }
        } catch (Exception e) {
            logger.error("获取Bundle ClassLoader失败: {}", bundle.getSymbolicName(), e);
            return null;
        }
    }

    @Override
    public boolean isBundleActive(Bundle bundle) {
        if (bundle == null) {
            return false;
        }
        return bundle.getState() == Bundle.ACTIVE;
    }

    /**
     * 获取所有Bundle信息
     *
     * @return Bundle信息映射（符号名称 -> Bundle状态）
     */
    public java.util.Map<String, String> getAllBundleInfo() {
        java.util.Map<String, String> bundleInfo = new java.util.HashMap<>();

        if (bundleContext == null) {
            logger.warn("BundleContext为空，无法获取Bundle信息");
            return bundleInfo;
        }

        try {
            Bundle[] bundles = bundleContext.getBundles();
            for (Bundle bundle : bundles) {
                String symbolicName = bundle.getSymbolicName();
                if (symbolicName != null) {
                    bundleInfo.put(symbolicName, getBundleStateName(bundle.getState()));
                }
            }
        } catch (Exception e) {
            logger.error("获取所有Bundle信息失败", e);
        }

        return bundleInfo;
    }

    /**
     * 获取Bundle详细信息
     *
     * @param symbolicName Bundle符号名称
     * @return Bundle详细信息
     */
    public java.util.Map<String, Object> getBundleDetails(String symbolicName) {
        java.util.Map<String, Object> details = new java.util.HashMap<>();

        Bundle bundle = findBundleBySymbolicName(symbolicName);
        if (bundle == null) {
            details.put("found", false);
            return details;
        }

        try {
            details.put("found", true);
            details.put("symbolicName", bundle.getSymbolicName());
            details.put("version", bundle.getVersion().toString());
            details.put("state", getBundleStateName(bundle.getState()));
            details.put("bundleId", bundle.getBundleId());
            details.put("location", bundle.getLocation());
            details.put("lastModified", new java.util.Date(bundle.getLastModified()));

            // 获取MANIFEST.MF头部信息
            java.util.Dictionary<String, String> headers = bundle.getHeaders();
            java.util.Map<String, String> headerMap = new java.util.HashMap<>();
            java.util.Enumeration<String> keys = headers.keys();
            while (keys.hasMoreElements()) {
                String key = keys.nextElement();
                headerMap.put(key, headers.get(key));
            }
            details.put("headers", headerMap);

        } catch (Exception e) {
            logger.error("获取Bundle详细信息失败: {}", symbolicName, e);
            details.put("error", e.getMessage());
        }

        return details;
    }

    /**
     * 检查Bundle是否存在
     *
     * @param symbolicName Bundle符号名称
     * @return 是否存在
     */
    public boolean bundleExists(String symbolicName) {
        return findBundleBySymbolicName(symbolicName) != null;
    }

    /**
     * 获取Bundle数量
     *
     * @return Bundle数量
     */
    public int getBundleCount() {
        if (bundleContext == null) {
            return 0;
        }
        return bundleContext.getBundles().length;
    }

    /**
     * 获取活动Bundle数量
     *
     * @return 活动Bundle数量
     */
    public int getActiveBundleCount() {
        if (bundleContext == null) {
            return 0;
        }

        int activeCount = 0;
        Bundle[] bundles = bundleContext.getBundles();
        for (Bundle bundle : bundles) {
            if (bundle.getState() == Bundle.ACTIVE) {
                activeCount++;
            }
        }
        return activeCount;
    }

    /**
     * 获取BundleContext
     *
     * @return BundleContext实例
     */
    public BundleContext getBundleContext() {
        return bundleContext;
    }
}
