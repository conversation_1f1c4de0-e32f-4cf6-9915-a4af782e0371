package com.fasnote.alm.plugin.manage.core;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.license.crypto.RSAKeyManager;
import com.fasnote.alm.license.crypto.RSALicenseEncryption;
import com.fasnote.alm.plugin.manage.model.PluginLicense;
import com.fasnote.alm.plugin.manage.model.ValidationResult;
import com.fasnote.alm.plugin.manage.security.SecurityValidator;
import com.fasnote.alm.plugin.manage.util.JsonUtil;

/**
 * 统一的许可证处理器 负责许可证文件的读取、解析、验证、解密和缓存管理
 */
public class UnifiedLicenseProcessor {

	private static final Logger logger = LoggerFactory.getLogger(UnifiedLicenseProcessor.class);
	private static final String LOG_PREFIX = "[UnifiedLicenseProcessor] ";

	// 许可证缓存
	private final Map<String, LicenseCache> licenseCache = new ConcurrentHashMap<>();

	// RSA加密处理器（复用实例）
	private final RSALicenseEncryption rsaEncryption;

	// 安全验证器
	private final SecurityValidator securityValidator;

	public UnifiedLicenseProcessor(SecurityValidator securityValidator) {
		try {
			RSAKeyManager keyManager = new RSAKeyManager();
			this.rsaEncryption = new RSALicenseEncryption(keyManager);
			this.securityValidator = securityValidator;
			if (this.securityValidator == null) {
				throw new IllegalArgumentException("SecurityValidator 不能为 null");
			}
			logger.info(LOG_PREFIX + "统一许可证处理器初始化完成");
		} catch (Exception e) {
			logger.error(LOG_PREFIX + "初始化许可证处理器失败", e);
			throw new RuntimeException("许可证处理器初始化失败", e);
		}
	}

	/**
	 * 清除所有缓存
	 */
	public void clearAllCache() {
		licenseCache.clear();
		logger.info(LOG_PREFIX + "已清除所有许可证缓存");
	}

	/**
	 * 清除许可证缓存
	 */
	public void clearCache(String pluginId) {
		licenseCache.remove(pluginId);
		logger.info(LOG_PREFIX + "已清除许可证缓存: {}", pluginId);
	}

	/**
	 * 获取缓存的许可证
	 */
	public LicenseCache getCachedLicense(String pluginId) {
		LicenseCache cached = licenseCache.get(pluginId);
		if (cached != null && cached.isValid()) {
			return cached;
		}
		return null;
	}

	/**
	 * 获取缓存统计信息
	 */
	public String getCacheStats() {
		return String.format("许可证缓存统计: 总数=%d, 详情=%s", licenseCache.size(), licenseCache.keySet());
	}

	/**
	 * 从内存数据加载许可证（用于兼容现有接口）
	 */
	public ValidationResult loadAndProcessLicense(String pluginId, byte[] licenseData) {
		logger.info(LOG_PREFIX + "从内存数据处理许可证: {}", pluginId);

		try {
			// 检查缓存
			LicenseCache cached = licenseCache.get(pluginId);
			if (cached != null && cached.getLicenseFilePath() == null) {
				logger.info(LOG_PREFIX + "使用缓存的内存许可证: {}", pluginId);
				return ValidationResult.success("许可证已缓存且有效");
			}

			// 解析JSON内容
			String licenseContent = new String(licenseData, "UTF-8");
			@SuppressWarnings("unchecked")
			Map<String, Object> licenseMap = JsonUtil.fromJson(licenseContent, Map.class);

			// 使用crypto模块验证和解密
			Map<String, byte[]> decryptedClasses = rsaEncryption.decryptAndVerifyLicenseFile(licenseMap);

			// 解析元数据
			PluginLicense licenseMetadata = JsonUtil.fromJson(licenseContent, PluginLicense.class);
			licenseMetadata.setRawLicenseData(licenseContent);

			// 业务验证
			ValidationResult businessValidation = validateLicenseBusiness(licenseMetadata);
			if (!businessValidation.isValid()) {
				return businessValidation;
			}

			// 缓存结果
			LicenseCache cache = new LicenseCache(pluginId, null, licenseMetadata, decryptedClasses, 0);
			licenseCache.put(pluginId, cache);

			logger.info(LOG_PREFIX + "内存许可证处理完成: {}", pluginId);
			return ValidationResult.success("许可证处理成功");

		} catch (Exception e) {
			String error = "内存许可证处理失败: " + e.getMessage();
			logger.error(LOG_PREFIX + error, e);
			return ValidationResult.failure(error);
		}
	}

	/**
	 * 统一的许可证加载和处理方法
	 *
	 * @param pluginId        插件ID
	 * @param licenseFilePath 许可证文件路径
	 * @return 验证结果
	 */
	public ValidationResult loadAndProcessLicense(String pluginId, String licenseFilePath) {
		logger.info(LOG_PREFIX + "开始处理许可证: {} -> {}", pluginId, licenseFilePath);

		try {
			// 1. 检查缓存是否有效
			LicenseCache cached = licenseCache.get(pluginId);
			if (cached != null && cached.isValid()) {
				logger.info(LOG_PREFIX + "使用缓存的许可证: {}", pluginId);
				return ValidationResult.success("许可证已缓存且有效");
			}

			// 2. 验证文件存在性
			File licenseFile = new File(licenseFilePath);
			if (!licenseFile.exists() || !licenseFile.isFile()) {
				String error = "许可证文件不存在: " + licenseFilePath;
				logger.error(LOG_PREFIX + error);
				return ValidationResult.failure(error);
			}

			// 3. 一次性读取许可证文件内容
			String licenseContent;
			try {
				licenseContent = new String(Files.readAllBytes(Paths.get(licenseFilePath)), "UTF-8");
				logger.info(LOG_PREFIX + "许可证文件读取成功: {}", pluginId);
			} catch (Exception e) {
				String error = "许可证文件读取失败: " + e.getMessage();
				logger.error(LOG_PREFIX + error, e);
				return ValidationResult.failure(error);
			}

			// 4. 解析许可证元数据（在crypto验证之前，确保数据一致性）
			PluginLicense licenseMetadata;
			try {
				licenseMetadata = JsonUtil.fromJson(licenseContent, PluginLicense.class);
				if (licenseMetadata == null) {
					throw new Exception("JSON解析返回null");
				}

				// 设置文件路径和原始数据
				licenseMetadata.setLicenseFilePath(licenseFilePath);
				licenseMetadata.setRawLicenseData(licenseContent);

				logger.info(LOG_PREFIX + "许可证元数据解析成功: {}", pluginId);
			} catch (Exception e) {
				String error = "许可证元数据解析失败: " + e.getMessage();
				logger.error(LOG_PREFIX + error, e);
				return ValidationResult.failure(error);
			}

			// 5. 业务验证（有效期、机器码等）- 在crypto验证之前进行
			ValidationResult businessValidation = validateLicenseBusiness(licenseMetadata);
			if (!businessValidation.isValid()) {
				logger.warn(LOG_PREFIX + "许可证业务验证失败: {} - {}", pluginId, businessValidation.getMessage());
				return businessValidation;
			}

			// 6. 使用crypto模块进行最终的密码学验证和解密
			Map<String, byte[]> decryptedClasses;
			try {
				decryptedClasses = rsaEncryption.decryptAndVerifyLicenseFile(licenseFilePath);
				logger.info(LOG_PREFIX + "许可证密码学验证和解密成功: {}", pluginId);
			} catch (Exception e) {
				String error = "许可证密码学验证或解密失败: " + e.getMessage();
				logger.error(LOG_PREFIX + error, e);
				return ValidationResult.failure(error);
			}

			// 7. 缓存处理结果
			LicenseCache cache = new LicenseCache(pluginId, licenseFilePath, licenseMetadata, decryptedClasses,
					licenseFile.lastModified());
			licenseCache.put(pluginId, cache);

			logger.info(LOG_PREFIX + "许可证处理完成并已缓存: {}", pluginId);
			return ValidationResult.success("许可证处理成功");

		} catch (Exception e) {
			String error = "许可证处理异常: " + e.getMessage();
			logger.error(LOG_PREFIX + error, e);
			return ValidationResult.failure(error);
		}
	}

	/**
	 * 业务验证逻辑（使用SecurityValidator）
	 */
	private ValidationResult validateLicenseBusiness(PluginLicense license) {
		if (license == null) {
			return ValidationResult.failure("许可证为空");
		}

		// 使用安全验证器进行完整的业务验证
		return securityValidator.validateLicense(license);
	}
}
