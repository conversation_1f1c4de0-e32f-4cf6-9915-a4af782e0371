package com.fasnote.alm.plugin.manage.api;

import java.util.Map;

import com.fasnote.alm.plugin.manage.classloader.EncryptedClassLoader;
import com.fasnote.alm.plugin.manage.core.LicenseCache;
import com.fasnote.alm.plugin.manage.model.PluginLicense;

/**
 * 服务注册管理器接口
 * 负责服务的注册和管理
 */
public interface IServiceRegistrationManager {

    /**
     * 从许可证缓存中加载并注册实现类
     *
     * @param pluginId    插件ID
     * @param cache       许可证缓存
     * @param classLoader 加密类加载器
     */
    void loadAndRegisterImplementations(String pluginId, LicenseCache cache, EncryptedClassLoader classLoader);

    /**
     * 从许可证中创建实现类实例
     *
     * @param pluginId           插件ID
     * @param license            许可证对象
     * @param serviceInterface   服务接口
     * @param implementationName 实现类名称
     * @param classLoader        加密类加载器
     * @return 创建的实例，如果失败则返回null
     * @throws Exception 创建失败时抛出异常
     */
    Object createImplementationInstanceFromLicense(String pluginId, PluginLicense license,
                                                   Class<?> serviceInterface, String implementationName,
                                                   EncryptedClassLoader classLoader) throws Exception;

    /**
     * 注册服务实例到服务注册表
     *
     * @param serviceInterface   服务接口类
     * @param serviceInstance    服务实例
     * @param interfaceName      接口名称
     * @param implementationName 实现类名称
     * @param pluginId           插件ID
     */
    void registerServiceInstance(Class<?> serviceInterface, Object serviceInstance, String interfaceName,
                                String implementationName, String pluginId);



    /**
     * 获取已注册的服务统计信息
     *
     * @return 服务统计信息
     */
    Map<String, Object> getServiceStatistics();

    /**
     * 清理指定插件的服务注册
     *
     * @param pluginId 插件ID
     */
    void cleanupPluginServices(String pluginId);

    /**
     * 清理所有服务注册
     */
    void cleanupAllServices();
}
