package com.fasnote.alm.plugin.manage.core;

import java.util.Optional;

import javax.inject.Inject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.plugin.manage.api.IPluginRuntimeCoordinator;
import com.fasnote.alm.plugin.manage.api.IRuntimeEnvironmentManager;
import com.fasnote.alm.plugin.manage.classloader.EncryptedClassLoader;

/**
 * 运行时环境管理器
 * 负责设置和管理插件的运行时环境，委托给PluginRuntimeCoordinator处理
 */
public class RuntimeEnvironmentManager implements IRuntimeEnvironmentManager {

    private static final Logger logger = LoggerFactory.getLogger(RuntimeEnvironmentManager.class);

    // 插件运行时协调器
    private final IPluginRuntimeCoordinator pluginRuntimeCoordinator;

    /**
     * 构造函数（使用依赖注入）
     *
     * @param pluginRuntimeCoordinator 插件运行时协调器
     */
    @Inject
    public RuntimeEnvironmentManager(IPluginRuntimeCoordinator pluginRuntimeCoordinator) {
        this.pluginRuntimeCoordinator = pluginRuntimeCoordinator;
    }

    /**
     * 从许可证缓存设置运行时环境（主要实现）
     *
     * @param pluginId 插件ID
     * @param cache    许可证缓存
     */
    @Override
    public void setupRuntimeEnvironment(String pluginId, LicenseCache cache) {
        pluginRuntimeCoordinator.setupPluginRuntime(pluginId, cache);
    }

    /**
     * 从原始许可证数据设置运行时环境（兼容性方法）
     *
     * @param pluginId        插件ID
     * @param rawLicenseData  原始许可证数据
     */
    @Override
    public void setupRuntimeEnvironment(String pluginId, byte[] rawLicenseData) {
        pluginRuntimeCoordinator.setupPluginRuntime(pluginId, rawLicenseData);
    }

    /**
     * 设置运行时环境 从许可证数据设置运行时环境（兼容性包装器）
     * 内部优先使用缓存，如果缓存不可用则解析许可证数据
     *
     * @param pluginId    插件ID
     * @param licenseData 许可证数据
     */
    public void setupRuntimeEnvironment(String pluginId, String licenseData) {
        try {
            pluginRuntimeCoordinator.setupPluginRuntime(pluginId, licenseData.getBytes("UTF-8"));
        } catch (Exception e) {
            logger.error("从许可证数据设置运行时环境失败: {}", pluginId, e);
        }
    }

    /**
     * 确保插件的运行时环境已经初始化
     * 如果没有初始化，则自动进行初始化
     *
     * @param pluginId 插件ID
     * @return 是否成功初始化或已经初始化
     */
    @Override
    public boolean ensureRuntimeEnvironmentInitialized(String pluginId) {
        return pluginRuntimeCoordinator.ensurePluginRuntimeInitialized(pluginId);
    }

    /**
     * 获取插件的加密类加载器
     *
     * @param pluginId 插件ID
     * @return 加密类加载器的Optional包装，如果不存在则返回empty
     */
    @Override
    public Optional<EncryptedClassLoader> getEncryptedClassLoader(String pluginId) {
        return pluginRuntimeCoordinator.getPluginClassLoader(pluginId);
    }

    /**
     * 清理指定插件的运行时环境
     *
     * @param pluginId 插件ID
     */
    @Override
    public void cleanupRuntimeEnvironment(String pluginId) {
        pluginRuntimeCoordinator.cleanupPluginRuntime(pluginId);
    }

    /**
     * 清理所有运行时环境
     */
    @Override
    public void cleanupAllRuntimeEnvironments() {
        pluginRuntimeCoordinator.cleanupAllPluginRuntimes();
    }

    /**
     * 获取已管理的运行时环境数量
     *
     * @return 运行时环境数量
     */
    @Override
    public int getRuntimeEnvironmentCount() {
        return pluginRuntimeCoordinator.getPluginRuntimeCount();
    }

    /**
     * 检查插件是否有运行时环境
     *
     * @param pluginId 插件ID
     * @return 是否存在运行时环境
     */
    @Override
    public boolean hasRuntimeEnvironment(String pluginId) {
        return pluginRuntimeCoordinator.hasPluginRuntime(pluginId);
    }
}
