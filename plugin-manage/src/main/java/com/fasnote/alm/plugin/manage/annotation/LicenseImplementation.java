package com.fasnote.alm.plugin.manage.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import com.fasnote.alm.injection.annotations.AliasFor;
import com.fasnote.alm.injection.annotations.Service;

/**
 * 许可证实现注解
 * 标记需要许可证验证的类，自动包含 @Service 注解功能
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Service
public @interface LicenseImplementation {

	/**
	 * 功能描述
	 */
	String description() default "";

	/**
	 * 许可证级别
	 */
	String level() default "STANDARD";

	/**
	 * 是否为高级功能
	 */
	boolean premium() default false;

	/**
	 * 服务接口类型（继承自 @Service）
	 * 如果不指定，将自动推断
	 */
	@AliasFor(value = "interfaces", annotation = Service.class)
	Class<?>[] interfaces() default {};

	/**
	 * 服务名称（继承自 @Service）
	 */
	@AliasFor(value = "value", annotation = Service.class)
	String serviceName() default "";

	/**
	 * 生命周期（继承自 @Service）
	 * 通过@AliasFor机制将此属性映射到@Service注解的scope属性
	 */
	@AliasFor(value = "scope", annotation = Service.class)
	Service.Scope scope() default Service.Scope.SINGLETON;
}