package com.fasnote.alm.plugin.manage.exception;

/**
 * 许可证文件未找到异常
 * 当无法找到指定的许可证文件时抛出此异常
 */
public class LicenseFileNotFoundException extends LicenseException {

    private static final long serialVersionUID = 1L;

    /**
     * 构造函数
     *
     * @param pluginId 插件ID
     */
    public LicenseFileNotFoundException(String pluginId) {
        super(String.format("未找到插件 %s 的许可证文件", pluginId), "LICENSE_FILE_NOT_FOUND", pluginId);
    }

    /**
     * 构造函数
     *
     * @param pluginId 插件ID
     * @param filePath 文件路径
     */
    public LicenseFileNotFoundException(String pluginId, String filePath) {
        super(String.format("未找到插件 %s 的许可证文件: %s", pluginId, filePath), "LICENSE_FILE_NOT_FOUND", pluginId, filePath);
    }

    /**
     * 构造函数
     *
     * @param pluginId 插件ID
     * @param filePath 文件路径
     * @param cause    原因异常
     */
    public LicenseFileNotFoundException(String pluginId, String filePath, Throwable cause) {
        super(String.format("无法访问插件 %s 的许可证文件: %s", pluginId, filePath), "LICENSE_FILE_NOT_FOUND", cause);
        setParameters(pluginId, filePath);
    }

    /**
     * 创建许可证目录不存在异常
     *
     * @param directoryPath 目录路径
     * @return 异常实例
     */
    public static LicenseFileNotFoundException directoryNotFound(String directoryPath) {
        return new LicenseFileNotFoundException("", directoryPath) {
            @Override
            public String getMessage() {
                return String.format("许可证目录不存在: %s", directoryPath);
            }
        };
    }

    /**
     * 创建许可证文件读取失败异常
     *
     * @param pluginId 插件ID
     * @param filePath 文件路径
     * @param cause    原因异常
     * @return 异常实例
     */
    public static LicenseFileNotFoundException readFailed(String pluginId, String filePath, Throwable cause) {
        return new LicenseFileNotFoundException(pluginId, filePath, cause);
    }
}
