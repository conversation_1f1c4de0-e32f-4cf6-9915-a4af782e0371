package com.fasnote.alm.plugin.manage.example;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.injection.facade.DI;
import com.fasnote.alm.plugin.manage.api.ILicenseManager;
import com.fasnote.alm.plugin.manage.web.service.PluginManagementService;

/**
 * 依赖注入使用示例
 * 展示如何正确使用重构后的DI架构
 */
public class DIUsageExample {

    private static final Logger logger = LoggerFactory.getLogger(DIUsageExample.class);

    /**
     * 示例：如何初始化和使用DI框架
     */
    public static void main(String[] args) {
        try {
            // 1. 初始化DI框架并安装模块
            initializeDI();

            // 2. 通过DI框架获取服务（推荐方式）
            demonstrateCorrectUsage();

            // 3. 展示错误的使用方式（仅作对比）
            demonstrateIncorrectUsage();

        } catch (Exception e) {
            logger.error("DI使用示例执行失败", e);
        }
    }

    /**
     * 初始化DI框架
     */
    private static void initializeDI() {
        logger.info("初始化DI框架...");

        // TODO: 通过适当的方式初始化DI框架和模块
        // 实际应用中，DI框架的初始化通常在应用启动时完成
        // 例如在OSGi Activator或Spring ApplicationContext中
        logger.info("DI框架应该在应用启动时初始化，这里仅作演示");

        logger.info("DI框架初始化完成");
    }

    /**
     * 演示正确的DI使用方式
     */
    private static void demonstrateCorrectUsage() {
        logger.info("=== 正确的DI使用方式 ===");

        // ✅ 方式1：通过DI门面获取服务（最简单）
        ILicenseManager licenseManager = DI.get(ILicenseManager.class);
        logger.info("获取到LicenseManager: {}", licenseManager.getClass().getSimpleName());

        // ✅ 方式2：通过DI门面获取Web服务
        PluginManagementService webService = DI.get(PluginManagementService.class);
        logger.info("获取到PluginManagementService: {}", webService.getClass().getSimpleName());

        // ✅ 方式3：在组件内部使用构造函数注入（推荐）
        // 这种方式在实际的业务组件中使用，如PluginManagementServiceImpl
        logger.info("组件内部使用构造函数注入，参见PluginManagementServiceImpl");
    }

    /**
     * 演示错误的DI使用方式（仅作对比）
     */
    private static void demonstrateIncorrectUsage() {
        logger.info("=== 错误的DI使用方式（已修复） ===");

        // ❌ 错误方式1：手动new对象
        logger.warn("❌ 错误：手动new对象会绕过DI框架");
        // SecurityValidator validator = new SecurityValidator(); // 错误！

        // ❌ 错误方式2：在构造函数中调用DI.get()
        logger.warn("❌ 错误：在构造函数中调用DI.get()违反IoC原则");
        // public MyService() { this.dependency = DI.get(SomeDependency.class); } // 错误！

        // ✅ 正确方式：使用构造函数注入
        logger.info("✅ 正确：使用构造函数注入，让DI框架管理依赖");
        // @Inject
        // public MyService(SomeDependency dependency) { this.dependency = dependency; } // 正确！
    }

    /**
     * 示例：如何在Web应用中使用
     */
    public static class WebApplicationExample {

        /**
         * 在Web控制器中使用DI服务
         */
        public void handleWebRequest() {
            // ✅ 在Web层获取业务服务
            PluginManagementService service = DI.get(PluginManagementService.class);
            
            // 使用服务处理业务逻辑
            var plugins = service.getAllPlugins();
            logger.info("获取到 {} 个插件", plugins.size());
        }
    }

    /**
     * 示例：如何在测试中使用
     */
    public static class TestExample {

        /**
         * 单元测试中的DI使用
         */
        public void testWithDI() {
            // ✅ 测试中可以使用mock对象替换真实依赖
            // 通过DI框架的测试模块功能实现
            logger.info("测试中使用DI框架，可以轻松mock依赖");
        }
    }
}
