package com.fasnote.alm.plugin.manage.api;

import java.util.Optional;

import com.fasnote.alm.plugin.manage.classloader.EncryptedClassLoader;
import com.fasnote.alm.plugin.manage.core.LicenseCache;

/**
 * 运行时环境管理器接口
 * 负责设置和管理插件的运行时环境，包括类加载器创建和服务注册
 */
public interface IRuntimeEnvironmentManager {

    /**
     * 从许可证缓存设置运行时环境（主要实现）
     *
     * @param pluginId 插件ID
     * @param cache    许可证缓存对象
     */
    void setupRuntimeEnvironment(String pluginId, LicenseCache cache);

    /**
     * 从原始许可证数据设置运行时环境（兼容性方法）
     *
     * @param pluginId        插件ID
     * @param rawLicenseData  原始许可证数据
     */
    void setupRuntimeEnvironment(String pluginId, byte[] rawLicenseData);

    /**
     * 获取插件的加密类加载器
     *
     * @param pluginId 插件ID
     * @return 加密类加载器的Optional包装
     */
    Optional<EncryptedClassLoader> getEncryptedClassLoader(String pluginId);

    /**
     * 清理指定插件的运行时环境
     *
     * @param pluginId 插件ID
     */
    void cleanupRuntimeEnvironment(String pluginId);

    /**
     * 清理所有插件的运行时环境
     */
    void cleanupAllRuntimeEnvironments();

    /**
     * 获取运行时环境数量
     *
     * @return 当前管理的运行时环境数量
     */
    int getRuntimeEnvironmentCount();

    /**
     * 检查插件是否有运行时环境
     *
     * @param pluginId 插件ID
     * @return 是否存在运行时环境
     */
    boolean hasRuntimeEnvironment(String pluginId);

    /**
     * 确保插件运行时环境已初始化
     *
     * @param pluginId 插件ID
     * @return 是否成功初始化或已经初始化
     */
    boolean ensureRuntimeEnvironmentInitialized(String pluginId);
}
