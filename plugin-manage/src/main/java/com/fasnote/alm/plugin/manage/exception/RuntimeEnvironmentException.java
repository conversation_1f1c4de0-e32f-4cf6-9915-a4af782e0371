package com.fasnote.alm.plugin.manage.exception;

/**
 * 运行时环境设置失败异常
 * 当设置插件运行时环境过程中发生错误时抛出此异常
 */
public class RuntimeEnvironmentException extends LicenseException {

    private static final long serialVersionUID = 1L;

    /**
     * 构造函数
     *
     * @param message  异常消息
     * @param pluginId 插件ID
     */
    public RuntimeEnvironmentException(String message, String pluginId) {
        super(message, "RUNTIME_ENVIRONMENT_FAILED", pluginId);
    }

    /**
     * 构造函数
     *
     * @param message  异常消息
     * @param pluginId 插件ID
     * @param cause    原因异常
     */
    public RuntimeEnvironmentException(String message, String pluginId, Throwable cause) {
        super(message, "RUNTIME_ENVIRONMENT_FAILED", cause);
        setParameters(pluginId);
    }

    /**
     * 创建类加载器创建失败异常
     *
     * @param pluginId 插件ID
     * @param cause    原因异常
     * @return 异常实例
     */
    public static RuntimeEnvironmentException classLoaderCreationFailed(String pluginId, Throwable cause) {
        return new RuntimeEnvironmentException("创建类加载器失败", pluginId, cause);
    }

    /**
     * 创建服务注册失败异常
     *
     * @param pluginId 插件ID
     * @param cause    原因异常
     * @return 异常实例
     */
    public static RuntimeEnvironmentException serviceRegistrationFailed(String pluginId, Throwable cause) {
        return new RuntimeEnvironmentException("服务注册失败", pluginId, cause);
    }

    /**
     * 创建许可证缓存不可用异常
     *
     * @param pluginId 插件ID
     * @return 异常实例
     */
    public static RuntimeEnvironmentException licenseCacheUnavailable(String pluginId) {
        return new RuntimeEnvironmentException("许可证缓存不可用", pluginId);
    }

    /**
     * 创建运行时环境初始化失败异常
     *
     * @param pluginId 插件ID
     * @param cause    原因异常
     * @return 异常实例
     */
    public static RuntimeEnvironmentException initializationFailed(String pluginId, Throwable cause) {
        return new RuntimeEnvironmentException("运行时环境初始化失败", pluginId, cause);
    }

    /**
     * 创建运行时环境清理失败异常
     *
     * @param pluginId 插件ID
     * @param cause    原因异常
     * @return 异常实例
     */
    public static RuntimeEnvironmentException cleanupFailed(String pluginId, Throwable cause) {
        return new RuntimeEnvironmentException("运行时环境清理失败", pluginId, cause);
    }

    /**
     * 创建依赖管理器不可用异常
     *
     * @param pluginId      插件ID
     * @param managerType   管理器类型
     * @return 异常实例
     */
    public static RuntimeEnvironmentException managerUnavailable(String pluginId, String managerType) {
        return new RuntimeEnvironmentException(
            String.format("%s 管理器不可用", managerType), pluginId
        );
    }
}
