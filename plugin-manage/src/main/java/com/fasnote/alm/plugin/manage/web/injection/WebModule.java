package com.fasnote.alm.plugin.manage.web.injection;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.injection.api.IBinder;
import com.fasnote.alm.injection.api.IModule;
import com.fasnote.alm.plugin.manage.web.service.PluginManagementService;
import com.fasnote.alm.plugin.manage.web.service.impl.PluginManagementServiceImpl;

/**
 * Web层依赖注入模块
 * 负责配置Web组件的依赖注入
 */
public class WebModule implements IModule {

    private static final Logger logger = LoggerFactory.getLogger(WebModule.class);

    @Override
    public void configure(IBinder binder) {
        logger.info("配置Web层依赖注入模块...");

        // 注册Web服务实现
        registerWebServices(binder);

        logger.info("Web层依赖注入模块配置完成");
    }

    /**
     * 注册Web服务
     */
    private void registerWebServices(IBinder binder) {
        logger.debug("注册Web服务...");

        // 注册PluginManagementService实现（使用自动依赖注入）
        binder.bind(PluginManagementServiceImpl.class).asSingleton().build();
        binder.bind(PluginManagementService.class, PluginManagementServiceImpl.class).asSingleton().build();

        logger.debug("Web服务注册完成");
    }

    @Override
    public String getName() {
        return "WebModule";
    }

    @Override
    public int getPriority() {
        return 20; // 较低优先级，在核心模块之后加载
    }
}
