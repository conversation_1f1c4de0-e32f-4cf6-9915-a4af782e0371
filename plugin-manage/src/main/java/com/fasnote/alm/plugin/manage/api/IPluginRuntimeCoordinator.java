package com.fasnote.alm.plugin.manage.api;

import java.util.Optional;

import com.fasnote.alm.plugin.manage.classloader.EncryptedClassLoader;
import com.fasnote.alm.plugin.manage.core.LicenseCache;

/**
 * 插件运行时协调器接口
 * 负责协调插件的运行时环境设置和服务注册
 * 
 * 这个接口解决了RuntimeEnvironmentManager和ServiceRegistrationManager之间的循环依赖：
 * - RuntimeEnvironmentManager专注于类加载器管理
 * - ServiceRegistrationManager专注于服务注册
 * - PluginRuntimeCoordinator协调两者的交互
 */
public interface IPluginRuntimeCoordinator {

    /**
     * 设置插件的完整运行时环境
     * 包括创建类加载器和注册服务
     *
     * @param pluginId 插件ID
     * @param cache    许可证缓存对象
     */
    void setupPluginRuntime(String pluginId, LicenseCache cache);

    /**
     * 设置插件的完整运行时环境（从原始数据）
     *
     * @param pluginId        插件ID
     * @param rawLicenseData  原始许可证数据
     */
    void setupPluginRuntime(String pluginId, byte[] rawLicenseData);

    /**
     * 获取插件的加密类加载器
     *
     * @param pluginId 插件ID
     * @return 加密类加载器的Optional包装
     */
    Optional<EncryptedClassLoader> getPluginClassLoader(String pluginId);

    /**
     * 清理插件的运行时环境
     * 包括清理类加载器和服务注册
     *
     * @param pluginId 插件ID
     */
    void cleanupPluginRuntime(String pluginId);

    /**
     * 清理所有插件的运行时环境
     */
    void cleanupAllPluginRuntimes();

    /**
     * 确保插件运行时环境已初始化
     *
     * @param pluginId 插件ID
     * @return 是否成功初始化或已经初始化
     */
    boolean ensurePluginRuntimeInitialized(String pluginId);

    /**
     * 检查插件是否有运行时环境
     *
     * @param pluginId 插件ID
     * @return 是否存在运行时环境
     */
    boolean hasPluginRuntime(String pluginId);

    /**
     * 获取已管理的插件运行时环境数量
     *
     * @return 运行时环境数量
     */
    int getPluginRuntimeCount();
}
