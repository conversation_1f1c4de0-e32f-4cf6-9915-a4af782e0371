package com.fasnote.alm.plugin.manage.exception;

/**
 * 类加载器创建失败异常
 * 当创建加密类加载器过程中发生错误时抛出此异常
 */
public class ClassLoaderCreationException extends LicenseException {

    private static final long serialVersionUID = 1L;

    /**
     * 构造函数
     *
     * @param message  异常消息
     * @param pluginId 插件ID
     */
    public ClassLoaderCreationException(String message, String pluginId) {
        super(message, "CLASSLOADER_CREATION_FAILED", pluginId);
    }

    /**
     * 构造函数
     *
     * @param message  异常消息
     * @param pluginId 插件ID
     * @param cause    原因异常
     */
    public ClassLoaderCreationException(String message, String pluginId, Throwable cause) {
        super(message, "CLASSLOADER_CREATION_FAILED", cause);
        setParameters(pluginId);
    }

    /**
     * 创建JAR创建失败异常
     *
     * @param pluginId 插件ID
     * @param cause    原因异常
     * @return 异常实例
     */
    public static ClassLoaderCreationException jarCreationFailed(String pluginId, Throwable cause) {
        return new ClassLoaderCreationException("创建JAR包失败", pluginId, cause);
    }

    /**
     * 创建解密失败异常
     *
     * @param pluginId 插件ID
     * @param cause    原因异常
     * @return 异常实例
     */
    public static ClassLoaderCreationException decryptionFailed(String pluginId, Throwable cause) {
        return new ClassLoaderCreationException("许可证解密失败", pluginId, cause);
    }

    /**
     * 创建父类加载器获取失败异常
     *
     * @param pluginId 插件ID
     * @return 异常实例
     */
    public static ClassLoaderCreationException parentClassLoaderNotFound(String pluginId) {
        return new ClassLoaderCreationException("无法获取父类加载器", pluginId);
    }
}
