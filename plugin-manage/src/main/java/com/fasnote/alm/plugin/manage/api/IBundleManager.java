package com.fasnote.alm.plugin.manage.api;

import org.osgi.framework.Bundle;

/**
 * Bundle管理器接口
 * 负责OSGi Bundle相关操作
 */
public interface IBundleManager {

    /**
     * 根据插件ID查找Bundle
     *
     * @param pluginId 插件ID
     * @return 找到的Bundle，如果未找到则返回null
     */
    Bundle findBundleByPluginId(String pluginId);

    /**
     * 根据符号名称查找Bundle
     *
     * @param symbolicName Bundle符号名称
     * @return 找到的Bundle，如果未找到则返回null
     */
    Bundle findBundleBySymbolicName(String symbolicName);

    /**
     * 检查Bundle是否需要许可证验证
     *
     * @param bundle   Bundle对象
     * @param pluginId 插件ID（用于日志）
     * @return 是否需要许可证验证
     */
    boolean isPluginRequiresLicense(Bundle bundle, String pluginId);

    /**
     * 检查插件是否需要许可证验证
     *
     * @param pluginId 插件ID
     * @return 是否需要许可证验证
     */
    boolean isPluginRequiresLicense(String pluginId);

    /**
     * 获取Bundle状态名称
     *
     * @param state Bundle状态
     * @return 状态名称
     */
    String getBundleStateName(int state);

    /**
     * 获取Bundle的ClassLoader
     *
     * @param bundle Bundle对象
     * @return Bundle的ClassLoader，如果获取失败则返回null
     */
    ClassLoader getBundleClassLoader(Bundle bundle);

    /**
     * 检查Bundle是否处于活动状态
     *
     * @param bundle Bundle对象
     * @return 是否活动
     */
    boolean isBundleActive(Bundle bundle);
}
