package com.fasnote.alm.plugin.manage.exception;

/**
 * Bundle未找到异常
 * 当无法找到指定的OSGi Bundle时抛出此异常
 */
public class BundleNotFoundException extends LicenseException {

    private static final long serialVersionUID = 1L;

    /**
     * 构造函数
     *
     * @param pluginId 插件ID
     */
    public BundleNotFoundException(String pluginId) {
        super(String.format("未找到插件 %s 对应的Bundle", pluginId), "BUNDLE_NOT_FOUND", pluginId);
    }

    /**
     * 构造函数
     *
     * @param pluginId     插件ID
     * @param symbolicName Bundle符号名称
     */
    public BundleNotFoundException(String pluginId, String symbolicName) {
        super(String.format("未找到插件 %s 对应的Bundle (符号名称: %s)", pluginId, symbolicName), "BUNDLE_NOT_FOUND", pluginId, symbolicName);
    }

    /**
     * 构造函数
     *
     * @param pluginId 插件ID
     * @param cause    原因异常
     */
    public BundleNotFoundException(String pluginId, Throwable cause) {
        super(String.format("查找插件 %s 对应的Bundle时发生异常", pluginId), "BUNDLE_NOT_FOUND", cause);
        setParameters(pluginId);
    }

    /**
     * 创建Bundle状态无效异常
     *
     * @param pluginId    插件ID
     * @param bundleState Bundle状态
     * @return 异常实例
     */
    public static BundleNotFoundException invalidState(String pluginId, String bundleState) {
        return new BundleNotFoundException(pluginId, bundleState) {
            @Override
            public String getMessage() {
                return String.format("插件 %s 的Bundle状态无效: %s", pluginId, bundleState);
            }
        };
    }

    /**
     * 创建Bundle上下文不可用异常
     *
     * @return 异常实例
     */
    public static BundleNotFoundException contextUnavailable() {
        return new BundleNotFoundException("") {
            @Override
            public String getMessage() {
                return "BundleContext不可用，无法查找Bundle";
            }
        };
    }

    /**
     * 创建Bundle ClassLoader获取失败异常
     *
     * @param pluginId 插件ID
     * @return 异常实例
     */
    public static BundleNotFoundException classLoaderUnavailable(String pluginId) {
        return new BundleNotFoundException(pluginId) {
            @Override
            public String getMessage() {
                return String.format("无法获取插件 %s 的Bundle ClassLoader", pluginId);
            }
        };
    }
}
