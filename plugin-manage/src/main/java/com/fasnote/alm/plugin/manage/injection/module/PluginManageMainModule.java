package com.fasnote.alm.plugin.manage.injection.module;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.injection.api.IBinder;
import com.fasnote.alm.injection.api.IModule;
import com.fasnote.alm.plugin.manage.web.injection.WebModule;

/**
 * 插件管理主模块
 * 组合所有子模块，提供完整的依赖注入配置
 */
public class PluginManageMainModule implements IModule {

    private static final Logger logger = LoggerFactory.getLogger(PluginManageMainModule.class);

    @Override
    public void configure(IBinder binder) {
        logger.info("配置插件管理主模块...");

        // 1. 配置核心许可证模块
        LicenseModule licenseModule = new LicenseModule();
        licenseModule.configure(binder);

        // 2. 配置Web层模块
        WebModule webModule = new WebModule();
        webModule.configure(binder);

        logger.info("插件管理主模块配置完成");
    }

    @Override
    public String getName() {
        return "PluginManageMainModule";
    }

    @Override
    public int getPriority() {
        return 0; // 最高优先级，作为主模块
    }
}
