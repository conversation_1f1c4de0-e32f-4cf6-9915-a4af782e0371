# License Management Framework v2.0

[![Version](https://img.shields.io/badge/version-2.0.0-blue.svg)](https://github.com/fasnote/alm-license-manage)
[![Java](https://img.shields.io/badge/java-11+-orange.svg)](https://openjdk.java.net/)
[![OSGi](https://img.shields.io/badge/OSGi-R6+-green.svg)](https://www.osgi.org/)
[![Polarion](https://img.shields.io/badge/Polarion-23.x+-purple.svg)](https://polarion.plm.automation.siemens.com/)

## 📋 目录

- [概述](#概述)
- [系统架构](#系统架构)
- [快速开始](#快速开始)
- [核心组件详解](#核心组件详解)
- [高级特性](#高级特性)
- [配置说明](#配置说明)
- [最佳实践](#最佳实践)
- [故障排除](#故障排除)
- [技术支持](#技术支持)

## 🚀 概述

企业级许可证管理和动态服务切换框架，专为Polarion ALM插件开发设计。通过加密许可证文件实现服务的动态注册、验证和切换，支持从默认实现到许可证实现的无缝升级。

### ✨ 核心功能
- 🔐 **许可证驱动的服务注册**：从加密许可证文件中自动提取和注册服务实现
- 🔄 **动态服务切换**：根据许可证状态自动在默认实现和许可证实现间切换
- 🔒 **加密类加载器**：支持从许可证中加载加密存储的接口和实现类
- 🔧 **多插件支持**：统一管理多个插件的许可证服务
- 🛡️ **FallbackModule 机制**：通过 ServiceLoader 自动发现和注册回退实现
- 🔌 **DI框架集成**：与现有依赖注入框架无缝集成
- 📋 **统一回退管理**：集中管理所有插件的回退实现，替代约定命名机制

## 🏗️ 系统架构

### 📊 许可证管理和动态服务切换流程图

```mermaid
graph TB
    subgraph "许可证文件"
        LF[License Files<br/>*.lic]
        LF --> |包含| SM[Service Mappings<br/>接口→实现类映射]
        LF --> |包含| ECD[Encrypted Class Data<br/>加密的实现类]
    end

    subgraph "模块配置阶段"
        LM[LicenseModule.configure]
        LM --> |1. 注册回退实现| RFI[registerFallbackImplementations]
        RFI --> |扫描| FMM[FallbackModuleManager]
        FMM --> |发现| FM[FallbackModule]
        FM --> |注册| FB[Fallback Implementation]

        LM --> |2. 注册许可证服务| RLS[registerLicenseServices]
        RLS --> |遍历| GPL[getRegisteredPluginIds]
        GPL --> |获取| PL[PluginLicense]
        PL --> |提取| SM
    end

    subgraph "服务注册流程"
        RLS --> |调用| RLS2[registerLicenseService]
        RLS2 --> |调用| LIC[loadInterfaceClass]
        LIC --> |优先使用| ECL[EncryptedClassLoader]
        ECL --> |加载| IC[Interface Class]
        LIC --> |回退到| SCL[System ClassLoader]

        RLS2 --> |调用| CIIL[createImplementationInstanceFromLicense]
        CIIL --> |创建| LI[License Implementation]
        RLS2 --> |注册到| DI[DI Framework]
        DI --> |覆盖| DEF[Default Implementation]
    end

    subgraph "运行时服务获取"
        APP[Application Code]
        APP --> |调用| DIG[DI.get(Interface.class)]
        DIG --> |返回| LI
        LI --> |如果失效| DEF
    end

    subgraph "核心组件"
        LMgr[LicenseManager<br/>许可证验证和管理]
        ECL2[EncryptedClassLoader<br/>加密类加载器]
        LASI[LicenseAwareServiceInterceptor<br/>许可证感知拦截器]
        PL2[PluginLicense<br/>许可证模型]
    end

    LM -.-> LMgr
    ECL -.-> ECL2
    RLS2 -.-> LSI
    FMM -.-> FMM2
    PL -.-> PL2

    style LF fill:#e1f5fe
    style LI fill:#c8e6c9
    style FB fill:#ffecb3
    style DI fill:#f3e5f5
    style FMM fill:#e8f5e8
    style FM fill:#fff3e0
```

### 🔑 关键特性

#### 1. 📋 **注册时决策机制**
- ⚡ 在模块配置阶段就决定使用哪个实现类
- 🚀 避免运行时的复杂拦截和类型转换
- 📈 提供更好的性能和稳定性

#### 2. 🔐 **加密类加载器支持**
- 🔒 支持从许可证中加载加密存储的接口类
- 🔄 优先使用 `EncryptedClassLoader`，失败时回退到系统类加载器
- 🌐 完整支持跨Bundle的类加载和实例化

#### 3. 🔧 **多插件统一管理**
- 🔍 自动扫描所有已注册插件的许可证
- 📋 从 `serviceMappings` 中提取接口到实现类的映射
- 🔗 支持多个插件的许可证服务同时注册

## 🚀 快速开始

### 1. 📝 业务插件中的接口定义

```java
// 定义服务接口
public interface IFeishuAuthenticatorEnhancer {
    Map<String, IAuthenticator> createEnhancedAuthenticatorMap(
        Map<String, IAuthenticator> originalAuthenticators);
    String getEnhancerType();
}

// 默认实现（总是可用）
public class FeishuDefaultAuthenticatorEnhancer implements IFeishuAuthenticatorEnhancer {
    @Override
    public Map<String, IAuthenticator> createEnhancedAuthenticatorMap(
            Map<String, IAuthenticator> originalAuthenticators) {
        // 返回原始认证器，不做增强
        return originalAuthenticators;
    }

    @Override
    public String getEnhancerType() {
        return "Default OAuth2 Enhancer";
    }
}
```

### 2. 🔐 许可证实现类（加密存储）

```java
// 许可证实现类（存储在加密的许可证文件中）
@LicenseImplementation(level = "PREMIUM", description = "飞书认证增强功能")
public class FeishuLicensedAuthenticatorEnhancer implements IFeishuAuthenticatorEnhancer {
    @Override
    @PremiumFeature(name = "feishu-authentication", requiredLevel = "PREMIUM")
    public Map<String, IAuthenticator> createEnhancedAuthenticatorMap(
            Map<String, IAuthenticator> originalAuthenticators) {
        // 提供完整的飞书认证增强功能
        Map<String, IAuthenticator> enhanced = new HashMap<>(originalAuthenticators);
        enhanced.put("oauth2", new FeishuOAuth2Authenticator(originalAuthenticators.get("oauth2")));
        return enhanced;
    }

    @Override
    public String getEnhancerType() {
        return "Licensed Premium Enhancer";
    }
}
```

### 3. 🔌 OSGi 服务注册

```java
import com.fasnote.alm.injection.api.IPackageScanProvider;

// 业务插件的包扫描提供者
public class FeishuPackageScanProvider implements IPackageScanProvider {
    @Override
    public String[] getScanPackages() {
        return new String[] { "com.fasnote.alm.auth.feishu" };
    }

    @Override
    public String getPluginId() {
        return "feishu-auth-plugin";
    }

    @Override
    public String getName() {
        return "Feishu Authentication Package Scanner";
    }
}

import com.fasnote.alm.injection.api.IPackageScanProvider;

// 在 Activator 中注册 OSGi 服务
public class Activator implements BundleActivator {
    public void start(BundleContext context) throws Exception {
        context.registerService(
            IPackageScanProvider.class,
            new FeishuPackageScanProvider(),
            null
        );
    }
}
```

**ServiceLoader 配置文件**：
```
# src/main/resources/META-INF/services/com.fasnote.alm.injection.api.IPackageScanProvider
com.fasnote.alm.auth.feishu.FeishuPackageScanProvider
```

### 4. 💼 服务使用

```java
// 在业务代码中使用服务
public class AuthenticationManager {

    public void enhanceAuthenticators() {
        // 通过DI框架获取服务（自动选择许可证实现或默认实现）
        IFeishuAuthenticatorEnhancer enhancer = DI.get(IFeishuAuthenticatorEnhancer.class);

        Map<String, IAuthenticator> originalAuthenticators = getOriginalAuthenticators();
        Map<String, IAuthenticator> enhanced = enhancer.createEnhancedAuthenticatorMap(originalAuthenticators);

        logger.info("使用增强器类型: {}", enhancer.getEnhancerType());
    }
}
```

## 🔧 核心组件详解

### 📦 LicenseModule - 许可证模块

负责许可证服务的动态注册和配置：

```java
public class LicenseModule implements IModule {

    @Override
    public void configure(IBinder binder) {
        // 1. 注册 FallbackModule 中的回退实现
        registerFallbackImplementations(binder);

        // 2. 动态注册许可证服务实现
        registerLicenseServices(binder);

        // 3. 注册许可证感知服务拦截器
        binder.registerInterceptor(new LicenseAwareServiceInterceptor(licenseManager));

        // 4. 注册其他许可证相关组件
        // ...
    }

    private void registerFallbackImplementations(IBinder binder) {
        // 创建 FallbackModuleManager
        FallbackModuleManager fallbackManager = new FallbackModuleManager();

        // 扫描并加载所有 FallbackModule
        fallbackManager.loadFallbackModules();

        // 将收集到的回退实现注册到 DI 容器
        fallbackManager.registerFallbackImplementations(binder);
    }

    private void registerLicenseServices(IBinder binder) {
        // 遍历所有插件许可证
        for (String pluginId : licenseManager.getRegisteredPluginIds()) {
            Optional<PluginLicense> licenseOpt = licenseManager.getPluginLicense(pluginId);
            if (!licenseOpt.isPresent() || !licenseOpt.get().isValid()) {
                continue; // 跳过无效许可证
            }

            PluginLicense license = licenseOpt.get();
            if (!license.hasServiceMappings()) {
                continue; // 跳过没有服务映射的许可证
            }

            // 提取服务映射
            Map<String, String> serviceMappings = license.getServiceMappings();

            // 注册每个服务
            for (Map.Entry<String, String> entry : serviceMappings.entrySet()) {
                registerLicenseService(binder, pluginId, entry.getKey(), entry.getValue());
            }
        }
    }
}
```

### 🛡️ LicenseManager - 许可证管理器

核心许可证管理和验证组件：

```java
public class LicenseManager {

    // 创建加密类加载器
    public Optional<EncryptedClassLoader> createEncryptedClassLoaderForPlugin(String pluginId) {
        PluginLicense license = pluginLicenses.get(pluginId);
        if (license != null && license.hasEncryptedClasses()) {
            return createEncryptedClassLoader(pluginId, license.getRawLicenseData());
        }
        return Optional.empty();
    }

    // 从许可证创建服务实例（已重构为createImplementationInstanceFromLicense）
    // 现在通过PluginRuntimeCoordinator统一管理插件运行时环境
}
```

### � FallbackModuleManager - 回退模块管理器

负责扫描、加载和管理所有的 IFallbackModule 实现：

```java
public class FallbackModuleManager {

    // 扫描并加载所有 FallbackModule
    public void loadFallbackModules() {
        // 使用 ServiceLoader 扫描 IFallbackModule 实现
        ServiceLoader<IFallbackModule> serviceLoader = ServiceLoader.load(IFallbackModule.class);

        for (IFallbackModule module : serviceLoader) {
            // 配置回退实现
            module.configureFallbacks(fallbackBinder);
            loadedModules.add(module);
        }

        // 按优先级排序
        loadedModules.sort((a, b) -> Integer.compare(a.getPriority(), b.getPriority()));
    }

    // 将收集到的回退实现注册到 DI 容器
    public void registerFallbackImplementations(IBinder binder) {
        Map<Class<?>, Class<?>> fallbackMappings = fallbackBinder.getFallbackMappings();

        for (Map.Entry<Class<?>, Class<?>> entry : fallbackMappings.entrySet()) {
            Class<?> serviceInterface = entry.getKey();
            Class<?> fallbackImpl = entry.getValue();

            // 注册为 "fallback" 命名服务
            binder.registerImplementation(serviceInterface, fallbackImpl, "fallback", false);
        }
    }
}
```

**核心特性**：
- 🔍 **自动发现**：通过 ServiceLoader 机制自动扫描所有 FallbackModule
- 📋 **统一注册**：将所有回退实现统一注册到 DI 容器
- 🔧 **优先级支持**：支持模块优先级排序
- 🔌 **插件化**：每个业务插件可独立定义自己的回退模块

### �️ LicenseServiceInterceptor - 许可证服务拦截器

负责在运行时根据许可证状态选择合适的服务实现：

```java
public class LicenseAwareServiceInterceptor implements IServiceInterceptor {

    @Override
    public Object beforeCreate(Class<?> serviceClass, IInjectionContext context) {
        // 获取接口的所有实现
        List<ServiceImplementationInfo> implementations = getImplementations(serviceClass);

        // 根据许可证状态选择合适的实现
        Class<?> selectedImplementation = selectImplementationByLicense(serviceClass, implementations);

        if (selectedImplementation != null) {
            // 许可证有效，返回许可证实现；无效则返回回退实现
            return selectedImplementation.getDeclaredConstructor().newInstance();
        }

        return null; // 让 DI 容器使用默认逻辑
    }

    private <T> T getFallbackService(Class<T> serviceClass, String serviceName) {
        // 1. 优先查找显式注册的 fallback 实现
        T fallbackService = dependencyInjector.getService(serviceClass, "fallback");
        if (fallbackService != null) {
            return fallbackService;
        }

        // 2. 向后兼容：使用约定命名查找
        String fallbackServiceName = serviceName != null ?
            serviceName + "-fallback" : serviceClass.getSimpleName() + "Fallback";

        return dependencyInjector.getService(serviceClass, fallbackServiceName);
    }
}
```

**改进特性**：
- ✅ **显式优先**：优先查找 "fallback" 命名的服务
- ✅ **向后兼容**：保留约定命名机制作为备选
- ✅ **统一管理**：与 FallbackModuleManager 配合工作
- ✅ **性能优化**：减少字符串拼接和查找次数

### ��🔐 EncryptedClassLoader - 加密类加载器

支持从许可证中加载加密存储的类：

```java
public class EncryptedClassLoader extends ClassLoader {

    // 加载加密的类
    @Override
    protected Class<?> findClass(String name) throws ClassNotFoundException {
        // 从缓存中查找解密后的类字节码
        byte[] classBytes = classBytesCache.get(name);
        if (classBytes != null) {
            return defineClass(name, classBytes, 0, classBytes.length, createProtectionDomain());
        }
        throw new ClassNotFoundException("类未找到: " + name);
    }

    // 添加解密后的JAR数据
    public void addDecryptedJar(String jarName, byte[] jarData) throws Exception {
        try (JarInputStream jarInput = new JarInputStream(new ByteArrayInputStream(jarData))) {
            JarEntry entry;
            while ((entry = jarInput.readNextJarEntry()) != null) {
                if (entry.getName().endsWith(".class")) {
                    String className = entry.getName().replace('/', '.').replace(".class", "");
                    byte[] classData = readAllBytes(jarInput);
                    classBytesCache.put(className, classData);
                }
            }
        }
    }
}
```

### 📄 PluginLicense - 许可证模型

许可证数据的完整模型：

```java
public class PluginLicense {

    // 服务接口和实现类的映射
    private Map<String, String> serviceMappings;

    // 加密的实现类数据
    private byte[] encryptedClassData;

    // 原始许可证数据
    private String rawLicenseData;

    // 检查是否包含服务映射
    public boolean hasServiceMappings() {
        return serviceMappings != null && !serviceMappings.isEmpty();
    }

    // 检查是否包含加密类
    public boolean hasEncryptedClasses() {
        return encryptedClassData != null && encryptedClassData.length > 0;
    }

    // 许可证验证
    public boolean isValid() {
        return expiryDate == null || expiryDate.isAfter(LocalDateTime.now());
    }
}
```

## 🚀 高级特性

### 1. 🎯 优先级和回退机制

系统提供完整的优先级和回退策略：

```java
// 服务获取的优先级顺序
public class ServiceResolutionFlow {

    public Object getService(Class<?> serviceInterface) {
        // 1. 优先使用许可证实现（如果可用且有效）
        Object licenseImpl = getLicenseImplementation(serviceInterface);
        if (licenseImpl != null && isLicenseValid(licenseImpl)) {
            return licenseImpl;
        }

        // 2. 回退到默认实现
        Object defaultImpl = getDefaultImplementation(serviceInterface);
        if (defaultImpl != null) {
            return defaultImpl;
        }

        // 3. 如果都没有，返回null
        return null;
    }
}
```

### 2. 🔧 多插件支持

框架支持同时管理多个插件的许可证服务：

```java
// 许可证文件结构示例
/*
licenses/
├── com.fasnote.alm.auth.feishu.lic          # 飞书插件许可证
├── com.fasnote.alm.reports.advanced.lic     # 高级报告插件许可证
└── com.fasnote.alm.workflow.custom.lic      # 自定义工作流插件许可证
*/

// 每个许可证文件包含的服务映射示例
{
    "pluginId": "com.fasnote.alm.auth.feishu",
    "serviceMappings": {
        "com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer":
            "com.fasnote.alm.auth.feishu.FeishuLicensedAuthenticatorEnhancer",
        "com.fasnote.alm.auth.feishu.IFeishuNotificationService":
            "com.fasnote.alm.auth.feishu.FeishuLicensedNotificationService"
    },
    "encryptedClassData": "base64-encoded-jar-data...",
    // ... 其他许可证字段
}
```

### 3. 🔄 动态类加载策略

框架提供智能的类加载策略：

```java
public class InterfaceClassLoader {

    private Class<?> loadInterfaceClass(String pluginId, String interfaceName) {
        // 1. 优先使用插件的加密类加载器
        Optional<EncryptedClassLoader> classLoaderOpt =
            licenseManager.createEncryptedClassLoaderForPlugin(pluginId);

        if (classLoaderOpt.isPresent()) {
            try {
                return classLoaderOpt.get().loadClass(interfaceName);
            } catch (ClassNotFoundException e) {
                // 继续尝试系统类加载器
            }
        }

        // 2. 回退到系统类加载器
        try {
            return Class.forName(interfaceName);
        } catch (ClassNotFoundException e) {
            return null;
        }
    }
}
```

### 4. 🛡️ 许可证验证和安全

完整的许可证验证机制：

```java
public class LicenseValidation {

    public ValidationResult validateLicense(PluginLicense license) {
        // 1. 基础验证
        if (license == null) {
            return ValidationResult.failure("许可证为空");
        }

        // 2. 时间验证
        if (license.getExpiryDate() != null &&
            license.getExpiryDate().isBefore(LocalDateTime.now())) {
            return ValidationResult.failure("许可证已过期");
        }

        // 3. 机器码验证
        if (!validateMachineCode(license.getMachineCode())) {
            return ValidationResult.failure("机器码不匹配");
        }

        // 4. 签名验证
        if (!validateSignature(license)) {
            return ValidationResult.failure("许可证签名无效");
        }

        return ValidationResult.success();
    }
}
```

## ⚙️ 配置说明

### 📁 许可证目录配置

```java
// 许可证文件存储目录
public class LicenseDirectoryManager {

    // 默认许可证目录
    private static final String DEFAULT_LICENSE_DIR =
        System.getProperty("polarion.home", "/opt/polarion") + "/licenses";

    // 获取许可证目录
    public File getLicenseDir() {
        String licenseDir = System.getProperty("license.directory", DEFAULT_LICENSE_DIR);
        File dir = new File(licenseDir);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        return dir;
    }
}
```

### 🔌 DI框架集成配置

```java
// 在业务插件的Activator中（新架构 - 无需手动注册）
public class FeishuActivator implements BundleActivator {

    @Override
    public void start(BundleContext context) throws Exception {
        logger.info("Feishu Authentication Plugin starting...");

        // 注意：回退实现由 FallbackModuleManager 自动管理
        // 无需手动注册 FeishuModule，FallbackModule 通过 ServiceLoader 自动发现

        logger.info("飞书回退实现由 manage 模块的 FallbackModuleManager 自动管理");
        logger.info("FeishuFallbackModule 将通过 ServiceLoader 机制自动发现和注册");

        logger.info("Feishu Authentication Plugin started successfully");
    }
}
```

**新架构优势**：
- ✅ **自动化**：无需手动注册模块，减少样板代码
- ✅ **解耦**：业务插件不依赖 DI 框架的具体实现
- ✅ **简化**：Activator 代码更简洁，职责更清晰

### 📄 许可证文件格式

```json
{
    "pluginId": "com.fasnote.alm.auth.feishu",
    "productName": "Feishu Integration Plugin",
    "version": "1.0.0",
    "licenseType": "PREMIUM",
    "issuer": "Fasnote Technology",

    "issueDate": "2024-01-01 00:00:00",
    "effectiveDate": "2024-01-01 00:00:00",
    "expiryDate": "2025-12-31 23:59:59",

    "licensedTo": "Enterprise Customer",
    "organization": "Example Corp",
    "maxUsers": 1000,

    "serviceMappings": {
        "com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer":
            "com.fasnote.alm.auth.feishu.FeishuLicensedAuthenticatorEnhancer"
    },

    "encryptedClassData": "base64-encoded-encrypted-jar-data...",
    "signature": "license-signature...",
    "contentHash": "content-hash..."
}
```

## 💡 最佳实践

### 1. 🎯 服务设计原则

#### 📋 接口设计
```java
// 良好的接口设计 - 简单明确
public interface IFeishuAuthenticatorEnhancer {
    Map<String, IAuthenticator> createEnhancedAuthenticatorMap(
        Map<String, IAuthenticator> originalAuthenticators);
    String getEnhancerType();
}

// 避免复杂的接口设计
public interface IBadInterface {
    void doEverything(Object... params); // 过于宽泛
    <T> T processGeneric(Class<T> type, Map<String, Object> config,
                        List<String> options, boolean flag); // 过于复杂
}
```

#### 🔧 实现类设计
```java
// 默认实现 - 总是可用，提供基础功能
public class FeishuDefaultAuthenticatorEnhancer implements IFeishuAuthenticatorEnhancer {
    @Override
    public Map<String, IAuthenticator> createEnhancedAuthenticatorMap(
            Map<String, IAuthenticator> originalAuthenticators) {
        // 返回原始认证器，不做增强
        return originalAuthenticators;
    }
}

// 许可证实现 - 提供完整功能
@LicenseImplementation(level = "PREMIUM")
public class FeishuLicensedAuthenticatorEnhancer implements IFeishuAuthenticatorEnhancer {
    @Override
    @PremiumFeature(name = "feishu-authentication", requiredLevel = "PREMIUM")
    public Map<String, IAuthenticator> createEnhancedAuthenticatorMap(
            Map<String, IAuthenticator> originalAuthenticators) {
        // 提供完整的飞书认证增强功能
        return enhanceWithFeishuSupport(originalAuthenticators);
    }
}
```

### 2. 🔐 许可证管理最佳实践

#### 📦 FallbackModule 注册（推荐方式）
```java
// 在业务插件中使用 FallbackModule 注册回退实现
public class FeishuFallbackModule implements IFallbackModule {
    @Override
    public void configureFallbacks(IFallbackBinder binder) {
        // 注册回退实现
        binder.registerFallback(
            IFeishuAuthenticatorEnhancer.class,
            FeishuDefaultAuthenticatorEnhancer.class
        );
    }

    @Override
    public String getPluginId() {
        return "com.fasnote.alm.auth.feishu";
    }
}
```

**优势**：
- ✅ 职责分离：manage 模块统一管理回退实现
- ✅ 显式注册：替代隐式的约定命名机制
- ✅ 自动发现：通过 ServiceLoader 机制自动扫描
- ✅ 可扩展性：其他插件可轻松添加回退模块

#### 🛡️ 许可证验证
```java
// 在许可证实现中进行适当的验证
@LicenseImplementation(level = "PREMIUM")
public class FeishuLicensedService implements IFeishuService {

    @Override
    @PremiumFeature(name = "feishu-integration", requiredLevel = "PREMIUM")
    public void performPremiumOperation() {
        // 框架会自动进行许可证验证
        // 如果验证失败，会自动回退到默认实现

        // 实现具体的业务逻辑
        doActualWork();
    }
}
```

### 3. 📊 错误处理和日志记录

#### 📝 审计日志
```java
public class LicenseAuditLogger {

    public void logServiceSwitch(String serviceInterface, String fromImpl, String toImpl) {
        auditLogger.info("[LICENSE] 服务切换: {} 从 {} 切换到 {}",
                        serviceInterface, fromImpl, toImpl);
    }

    public void logLicenseValidation(String pluginId, boolean isValid, String reason) {
        if (isValid) {
            auditLogger.info("[LICENSE] 许可证验证成功: {}", pluginId);
        } else {
            auditLogger.warn("[LICENSE] 许可证验证失败: {} - {}", pluginId, reason);
        }
    }
}
```

#### 🔄 优雅降级
```java
public class ServiceWithGracefulDegradation {

    public void performOperation() {
        try {
            // 尝试使用许可证功能
            IAdvancedService advancedService = DI.get(IAdvancedService.class);
            advancedService.performAdvancedOperation();

        } catch (LicenseException e) {
            // 许可证问题，使用基础功能
            logger.info("许可证功能不可用，使用基础功能: {}", e.getMessage());
            performBasicOperation();

        } catch (Exception e) {
            // 其他异常，记录并处理
            logger.error("操作执行失败", e);
            throw new ServiceException("操作失败", e);
        }
    }
}
```

## � 迁移指南

### 从 FeishuModule 迁移到 FallbackModule

如果你的项目中使用了旧的 `FeishuModule` 直接注册回退实现，请按以下步骤迁移：

#### 1. 创建 FallbackModule

**旧方式（已废弃）**：
```java
public class FeishuModule implements IModule {
    @Override
    public void configure(IBinder binder) {
        binder.registerImplementation(
            IFeishuAuthenticatorEnhancer.class,
            FeishuDefaultAuthenticatorEnhancer.class,
            "default",
            true
        );
    }
}
```

**新方式（推荐）**：
```java
public class FeishuFallbackModule implements IFallbackModule {
    @Override
    public void configureFallbacks(IFallbackBinder binder) {
        binder.registerFallback(
            IFeishuAuthenticatorEnhancer.class,
            FeishuDefaultAuthenticatorEnhancer.class
        );
    }

    @Override
    public String getPluginId() {
        return "com.fasnote.alm.auth.feishu";
    }

    @Override
    public String getName() {
        return "FeishuFallbackModule";
    }
}
```

#### 2. 添加 ServiceLoader 配置

创建文件 `src/main/resources/META-INF/services/com.fasnote.alm.plugin.manage.api.IFallbackModule`：
```
com.fasnote.alm.auth.feishu.injection.FeishuFallbackModule
```

#### 3. 移除旧的模块注册

在 `Activator` 中移除对 `FeishuModule` 的注册：
```java
// 移除这段代码
// FeishuModule feishuModule = new FeishuModule();
// DI.getInjector().installModule(feishuModule);
```

#### 4. 验证迁移

启动应用后，检查日志中是否有以下信息：
```
[INFO] 加载 FallbackModule: FeishuFallbackModule (插件: com.fasnote.alm.auth.feishu, 优先级: 100)
[INFO] 注册回退实现: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer -> com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer (name=fallback)
```

### 迁移优势

- ✅ **统一管理**：所有回退实现由 manage 模块统一管理
- ✅ **自动发现**：通过 ServiceLoader 自动扫描，无需手动注册
- ✅ **职责分离**：业务插件只关注回退实现定义，不涉及 DI 注册细节
- ✅ **向后兼容**：保留约定命名机制，确保平滑迁移

## �🔧 故障排除

### ❓ 常见问题

#### 1. ❌ 许可证服务未注册
```
错误信息: 接口类未找到，跳过动态注册: com.example.IService (插件: com.example.plugin)
```

**解决方案:**
- 检查许可证文件中的 `serviceMappings` 是否正确
- 确认接口类在加密类加载器或系统类路径中可用
- 验证许可证文件格式是否正确

#### 2. 🔐 加密类加载器创建失败
```
错误信息: 插件许可证缺少原始JSON数据: com.example.plugin
```

**解决方案:**
- 确保许可证文件完整且未损坏
- 检查许可证解密过程是否成功
- 验证 `rawLicenseData` 字段是否正确设置

#### 3. ⚠️ 服务实例创建失败
```
错误信息: 创建许可证服务实例失败: com.example.ServiceImpl (插件: com.example.plugin)
```

**解决方案:**
- 检查实现类是否有无参构造函数
- 确认实现类在加密JAR中存在
- 验证类的依赖关系是否满足

#### 4. 🔄 FallbackModule 未被发现
```
错误信息: FallbackModule 扫描完成，共加载 0 个模块
```

**解决方案:**
- 检查 ServiceLoader 配置文件是否存在：`META-INF/services/com.fasnote.alm.plugin.manage.api.IFallbackModule`
- 确认配置文件中的类名是否正确（包含完整包名）
- 验证 FallbackModule 实现类是否在类路径中
- 检查 FallbackModule 类是否有无参构造函数

#### 5. 🛡️ 回退服务未找到
```
错误信息: 未找到降级服务实现: IFeishuAuthenticatorEnhancer (查找名称: fallback, IFeishuAuthenticatorEnhancerFallback)
```

**解决方案:**
- 确认 FallbackModule 已正确注册回退实现
- 检查服务接口名称是否与注册时一致
- 验证回退实现类是否存在且可实例化
- 查看 FallbackModuleManager 的注册日志

#### 6. ⚠️ ServiceLoader 类加载问题
```
错误信息: ServiceConfigurationError: Provider class could not be instantiated
```

**解决方案:**
- 确保 FallbackModule 实现类有公共的无参构造函数
- 检查类的静态初始化是否有异常
- 验证所有依赖的类是否在类路径中
- 查看详细的异常堆栈信息

### 🔍 调试技巧

#### 📊 启用详细日志
```properties
# 在 logback.xml 中添加
<logger name="com.fasnote.alm.plugin.manage" level="DEBUG"/>
<logger name="com.fasnote.alm.plugin.manage.core.LicenseManager" level="DEBUG"/>
<logger name="com.fasnote.alm.plugin.manage.classloader.EncryptedClassLoader" level="DEBUG"/>
```

#### 🔍 检查许可证状态
```java
// 在业务代码中添加调试信息
public void debugLicenseStatus() {
    LicenseManager licenseManager = // 获取LicenseManager实例

    // 检查已注册的插件
    List<String> pluginIds = licenseManager.getRegisteredPluginIds();
    logger.info("已注册的插件: {}", pluginIds);

    // 检查每个插件的许可证状态
    for (String pluginId : pluginIds) {
        Optional<PluginLicense> license = licenseManager.getPluginLicense(pluginId);
        if (license.isPresent()) {
            PluginLicense l = license.get();
            logger.info("插件 {} - 有效: {}, 服务映射: {}, 加密类: {}",
                       pluginId, l.isValid(), l.hasServiceMappings(), l.hasEncryptedClasses());
        }
    }
}
```

## 📞 技术支持

### 📁 日志文件位置
- 主日志: `${polarion.home}/logs/polarion.log`
- 许可证审计日志: `${polarion.home}/logs/license-audit.log`

### 🏷️ 关键日志标识
- `[LicenseManager]` - 许可证管理相关日志
- `[EncryptedClassLoader]` - 加密类加载相关日志
- `[LicenseModule]` - 许可证模块配置相关日志

### 📋 版本信息
- **当前版本**: 2.0.0
- **支持的Java版本**: 11+
- **支持的OSGi版本**: R6+
- **支持的Polarion版本**: 23.x+
- **依赖的DI框架**: 自定义轻量级DI框架

### 🏗️ 架构特点
- **注册时决策**: 在模块配置阶段决定服务实现，避免运行时开销
- **加密类支持**: 完整支持从许可证中加载加密存储的类
- **多插件管理**: 统一管理多个插件的许可证服务
- **优雅回退**: 许可证失效时自动回退到默认实现
- **OSGi兼容**: 完全兼容OSGi环境和Bundle隔离

---

<div align="center">

**[⬆️ 返回顶部](#license-management-framework-v20)**

Made with ❤️ by Fasnote Technology

</div>