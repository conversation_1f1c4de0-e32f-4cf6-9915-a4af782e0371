# 许可证业务注册模块使用指南

## 概述

本指南说明基于 OSGi 服务的注解驱动许可证业务注册机制的使用方法。

## 架构特性

### 🎯 **核心特性**
- ✅ **注解驱动**: 使用 `@LicenseImplementation` 和 `@FallbackImplementation` 注解
- ✅ **OSGi 服务**: 基于 `IPackageScanProvider` 服务的包路径声明
- ✅ **自动扫描**: 自动发现和注册带注解的实现类
- ✅ **统一管理**: `LicenseBusinessRegistry` 统一管理许可证实现和回退实现

### 🔧 **技术特性**
- OSGi 服务跟踪机制
- ServiceLoader 兼容性支持
- 支持优先级管理
- 支持命名服务
- 线程安全
- 详细的注册统计

## 使用步骤

### 1. 添加注解到实现类

**回退实现类:**
```java
@FallbackImplementation(
    value = IFeishuAuthenticatorEnhancer.class,
    description = "飞书认证增强默认实现",
    priority = 100
)
public class FeishuDefaultAuthenticatorEnhancer implements IFeishuAuthenticatorEnhancer {
    // 实现代码
}
```

**许可证实现类:**
```java
@LicenseImplementation(
    level = "PREMIUM",
    description = "飞书认证增强功能",
    premium = true
)
public class FeishuLicensedAuthenticatorEnhancer implements IFeishuAuthenticatorEnhancer {
    // 实现代码
}
```

### 2. 创建包扫描提供者

```java
import com.fasnote.alm.injection.api.IPackageScanProvider;

public class FeishuPackageScanProvider implements IPackageScanProvider {
    @Override
    public String[] getScanPackages() {
        return new String[] { "com.fasnote.alm.auth.feishu" };
    }

    @Override
    public String getPluginId() {
        return "feishu-auth-plugin";
    }
}
```

### 3. 注册 OSGi 服务

在插件的 `Activator` 中注册服务：

```java
import com.fasnote.alm.injection.api.IPackageScanProvider;

packageScanProviderRegistration = context.registerService(
    IPackageScanProvider.class,
    new FeishuPackageScanProvider(),
    null
);
```

### 4. 添加 ServiceLoader 配置

创建文件：`META-INF/services/com.fasnote.alm.injection.api.IPackageScanProvider`
内容：`com.fasnote.alm.auth.feishu.FeishuPackageScanProvider`

## 注解参考

### @FallbackImplementation

```java
@FallbackImplementation(
    value = IServiceInterface.class,  // 必需：服务接口类
    name = "",                        // 可选：服务名称
    priority = 100,                   // 可选：优先级（数字越小优先级越高）
    description = "",                 // 可选：描述
    scope = Scope.SINGLETON          // 可选：生命周期
)
```

### @LicenseImplementation

```java
@LicenseImplementation(
    level = "PREMIUM",               // 许可证级别
    description = "",                // 功能描述
    premium = true                   // 是否为高级功能
)
```

## 优先级规则

1. **许可证实现**: 默认优先级 50（高优先级）
2. **回退实现**: 默认优先级 100（低优先级）
3. **数字越小优先级越高**
4. **运行时根据许可证状态选择合适的实现**

## 配置说明

### 📦 **OSGi 服务配置**
- **服务接口**: `IPackageScanProvider`
- **注册方式**: OSGi 服务注册 + ServiceLoader 配置
- **自动发现**: 通过服务跟踪器自动发现所有包扫描提供者

### 🔧 **插件责任**
每个插件需要：
1. 实现 `IPackageScanProvider` 接口
2. 在 Activator 中注册 OSGi 服务
3. 添加 ServiceLoader 配置文件

## 使用示例

### 基本使用
```java
// 1. 添加注解到实现类
@FallbackImplementation(IMyService.class)
public class MyFallbackService implements IMyService { }

@LicenseImplementation(level = "PREMIUM")
public class MyLicenseService implements IMyService { }

// 2. 创建包扫描提供者
import com.fasnote.alm.injection.api.IPackageScanProvider;

public class MyPackageScanProvider implements IPackageScanProvider {
    public String[] getScanPackages() {
        return new String[] { "com.example.service" };
    }
}

// 3. 注册 OSGi 服务
context.registerService(IPackageScanProvider.class, new MyPackageScanProvider(), null);

// 4. 服务会自动被发现和注册
// 5. 运行时根据许可证状态选择实现
```

## 故障排除

### 常见问题

1. **类未被扫描到**
   - 检查 `IPackageScanProvider` 服务是否注册成功
   - 确认包路径是否正确
   - 确认注解是否正确添加
   - 检查 ServiceLoader 配置文件是否存在
   - 查看日志中的扫描信息

2. **优先级不正确**
   - 检查 `priority` 属性设置
   - 确认许可证状态是否正确

3. **服务注入失败**
   - 检查接口和实现类的匹配
   - 确认 DI 容器配置正确

### 调试技巧

```java
// 查看注册统计
ILicenseBusinessRegistry.RegistrationStats stats = registry.getRegistrationStats();
logger.info("注册统计: {}", stats);

// 查看已注册的服务
List<Class<?>> interfaces = registry.getRegisteredServiceInterfaces();
logger.info("已注册服务: {}", interfaces);
```

## 最佳实践

1. **使用注解**: 优先使用注解驱动的方式
2. **合理设置优先级**: 许可证实现使用较小的数字
3. **添加描述**: 为注解添加有意义的描述
4. **实现包扫描提供者**: 每个插件实现自己的 `IPackageScanProvider`
5. **双重配置**: 同时配置 OSGi 服务和 ServiceLoader
6. **测试验证**: 确保许可证验证和回退机制正常工作
